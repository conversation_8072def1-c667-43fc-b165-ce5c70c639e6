// PayFast Payment Module - Server-side exports only
// This file should be used in API routes and server components

// Types
export * from './types';

// Services (Server-side only)
export { PayFastService } from './services/PayFastService';

// Utils (Server-side safe)
export { PayFastUtils } from './utils';

// Module Configuration (Server-side safe)
export const PayFastModule = {
  name: 'PayFast',
  version: '1.0.0',
  description: 'Complete PayFast payment integration module for StokvelMarket',
  provider: 'PayFast',
  country: 'South Africa',
  currency: 'ZAR',
  features: {
    standardPayments: true,
    recurringPayments: true,
    refunds: false, // Manual only
    webhooks: true,
    subscriptions: true,
    multiCurrency: false,
    mobileOptimized: true,
    groupPayments: false // Would need custom implementation
  },
  paymentMethods: [
    'Credit Cards (Visa, Mastercard, Amex)',
    'Instant EFT',
    'SnapScan',
    'Mobicred',
    'Bitcoin',
    'Standard Bank Instant Money',
    'Nedbank Pay-by-Link'
  ],
  fees: {
    creditCard: '2.9% + R2.00',
    eft: '1.45% (min R2.00, max R10.00)',
    bitcoin: '1.0%'
  },
  limits: {
    minAmount: 0.01,
    maxAmount: 1000000,
    currency: 'ZAR'
  },
  urls: {
    sandbox: 'https://sandbox.payfast.co.za/eng/process',
    production: 'https://www.payfast.co.za/eng/process',
    api: {
      sandbox: 'https://api.sandbox.payfast.co.za',
      production: 'https://api.payfast.co.za'
    }
  },
  documentation: {
    integration: '/PAYMENT_INTEGRATION_GUIDE.md',
    api: 'https://developers.payfast.co.za/docs',
    support: 'https://www.payfast.co.za/help'
  }
};

// Module Utilities (Server-side safe)
export const PayFastModuleUtils = {
  /**
   * Initialize PayFast module with configuration
   */
  initialize: (config: {
    merchantId: string;
    merchantKey: string;
    passphrase?: string;
    sandbox?: boolean;
    returnUrl: string;
    cancelUrl: string;
    notifyUrl: string;
  }) => {
    return new PayFastService({
      merchantId: config.merchantId,
      merchantKey: config.merchantKey,
      passphrase: config.passphrase,
      sandbox: config.sandbox ?? true,
      returnUrl: config.returnUrl,
      cancelUrl: config.cancelUrl,
      notifyUrl: config.notifyUrl
    });
  },

  /**
   * Validate module configuration
   */
  validateConfig: (config: any) => {
    return PayFastUtils.validateConfig(config);
  },

  /**
   * Get module information
   */
  getModuleInfo: () => PayFastModule,

  /**
   * Check if module supports feature
   */
  supportsFeature: (feature: keyof typeof PayFastModule.features) => {
    return PayFastModule.features[feature];
  },

  /**
   * Get supported payment methods
   */
  getPaymentMethods: () => PayFastModule.paymentMethods,

  /**
   * Get fee structure
   */
  getFees: () => PayFastModule.fees,

  /**
   * Get payment limits
   */
  getLimits: () => PayFastModule.limits,

  /**
   * Generate module-specific order ID
   */
  generateOrderId: (prefix?: string) => {
    return PayFastUtils.generateOrderId(prefix || 'PF');
  }
};

// Default export for server-side usage
export default {
  ...PayFastModule,
  utils: PayFastModuleUtils,
  service: PayFastService
};
