// Peach Payments Module - Main Export File

// Types
export * from './types';

// Services
export { PeachService } from './services/PeachService';

// Utils
export { PeachUtils } from './utils';

// Store
export {
  default as peachReducer,
  createPeachPayment,
  createRecurringPeachPayment,
  createPeachSplitPayment,
  fetchPeachPaymentHistory,
  checkPeachPaymentStatus,
  processPeachRefund,
  cancelPeachSubscription,
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory,
  updatePaymentInHistory,
  selectPeachState,
  selectPeachLoading,
  selectPeachCurrentPayment,
  selectPeachPaymentHistory,
  selectPeachError,
  selectPeachConfig
} from './store/peachSlice';

// Hooks
export {
  usePeach,
  usePeachStatusPolling,
  usePeachConfig,
  usePeachPaymentMethods
} from './hooks/usePeach';

// Components
export {
  PeachCheckout,
  PeachPaymentMethods,
  PeachStatus
} from './components';

// Module Configuration
export const PeachModule = {
  name: 'Peach Payments',
  version: '1.0.0',
  description: 'Complete Peach Payments integration module for StokvelMarket',
  provider: 'Peach Payments',
  countries: ['South Africa', 'Kenya', 'Mauritius'],
  currencies: ['ZAR', 'KES', 'MUR', 'USD', 'EUR', 'GBP'],
  features: {
    standardPayments: true,
    recurringPayments: true,
    refunds: true,
    webhooks: true,
    subscriptions: true,
    multiCurrency: true,
    mobileOptimized: true,
    groupPayments: true, // Native split payment support
    tokenization: true,
    fraudProtection: true,
    threeDSecure: true,
    embeddedCheckout: true
  },
  paymentMethods: [
    'Credit/Debit Cards (Visa, Mastercard, Amex, Diners)',
    'Capitec Pay',
    'Payflex (Buy Now Pay Later)',
    'ZeroPay (Buy Now Pay Later)',
    'Float (Buy Now Pay Later)',
    'Happy Pay (Buy Now Pay Later)',
    'Scan to Pay (QR Code)',
    'Apple Pay',
    'Google Pay',
    'Samsung Pay',
    'PayPal',
    'Mobicred',
    '1Voucher',
    'RCS Cards',
    'A+ Store Cards',
    'MoneyBadger (Crypto)',
    'Pay by Bank',
    'Peach EFT',
    'M-PESA (Kenya)',
    'blink by Emtel (Mauritius)',
    'MCB Juice (Mauritius)',
    'MauCAS (Mauritius)'
  ],
  fees: {
    creditCard: '2.4% + R1.50',
    eft: '0.95% (min R1.50, max R8.00)',
    alternativePayments: '1.5-2.5%',
    internationalCards: '3.4% + R2.00'
  },
  limits: {
    minAmount: 0.01,
    maxAmount: ********,
    currencies: ['ZAR', 'KES', 'MUR', 'USD', 'EUR', 'GBP']
  },
  urls: {
    sandbox: 'https://testapi-v2.peachpayments.com',
    production: 'https://api-v2.peachpayments.com',
    dashboard: {
      sandbox: 'https://test.peachpayments.com',
      production: 'https://peachpayments.com'
    }
  },
  documentation: {
    integration: '/PEACH_PAYMENTS_INTEGRATION_GUIDE.md',
    api: 'https://developer.peachpayments.com/docs/payments-api-overview',
    support: 'https://peachpayments.com/support'
  }
};

// Module Utilities
export const PeachModuleUtils = {
  /**
   * Initialize Peach module with configuration
   */
  initialize: (config: {
    entityId: string;
    username: string;
    password: string;
    sandbox?: boolean;
    successUrl: string;
    cancelUrl: string;
    webhookUrl?: string;
  }) => {
    return new PeachService({
      entityId: config.entityId,
      username: config.username,
      password: config.password,
      sandbox: config.sandbox ?? true,
      baseUrl: config.sandbox 
        ? 'https://testapi-v2.peachpayments.com'
        : 'https://api-v2.peachpayments.com',
      successUrl: config.successUrl,
      cancelUrl: config.cancelUrl,
      webhookUrl: config.webhookUrl
    });
  },

  /**
   * Validate module configuration
   */
  validateConfig: (config: any) => {
    return PeachUtils.validateConfig(config);
  },

  /**
   * Get module information
   */
  getModuleInfo: () => PeachModule,

  /**
   * Check if module supports feature
   */
  supportsFeature: (feature: keyof typeof PeachModule.features) => {
    return PeachModule.features[feature];
  },

  /**
   * Get supported payment methods
   */
  getPaymentMethods: (currency?: string, country?: string) => {
    return PeachUtils.filterPaymentMethods({ currency, country, enabled: true });
  },

  /**
   * Get fee structure
   */
  getFees: () => PeachModule.fees,

  /**
   * Get payment limits
   */
  getLimits: () => PeachModule.limits,

  /**
   * Generate module-specific order ID
   */
  generateOrderId: (prefix?: string) => {
    return PeachUtils.generateOrderId(prefix || 'PEACH');
  },

  /**
   * Get available currencies
   */
  getCurrencies: () => PeachModule.currencies,

  /**
   * Get supported countries
   */
  getCountries: () => PeachModule.countries,

  /**
   * Check if currency is supported
   */
  supportsCurrency: (currency: string) => {
    return PeachModule.currencies.includes(currency);
  },

  /**
   * Check if country is supported
   */
  supportsCountry: (country: string) => {
    return PeachModule.countries.includes(country);
  },

  /**
   * Get payment method by ID
   */
  getPaymentMethodInfo: (paymentBrand: string) => {
    return PeachUtils.getPaymentMethodConfig(paymentBrand as any);
  },

  /**
   * Check payment status codes
   */
  isPaymentSuccessful: (statusCode: string) => {
    return PeachUtils.isPaymentSuccessful(statusCode);
  },

  isPaymentFailed: (statusCode: string) => {
    return PeachUtils.isPaymentFailed(statusCode);
  },

  isPaymentPending: (statusCode: string) => {
    return PeachUtils.isPaymentPending(statusCode);
  }
};

// Default export
export default {
  ...PeachModule,
  utils: PeachModuleUtils,
  service: PeachService,
  components: {
    PeachCheckout,
    PeachPaymentMethods,
    PeachStatus
  }
};
