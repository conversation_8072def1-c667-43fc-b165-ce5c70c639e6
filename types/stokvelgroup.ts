// types/stokvelgroup.ts

/**
 * Mirrors the fields from IStokvelGroup in the Mongoose model,
 * but using front-end-friendly types (e.g., string for _id, etc.).
 */
export interface StokvelGroup {
    _id: string;              // Mongoose assigns string IDs on the front-end
    name: string;
    description: string;
    geolocation: string;
    members: string[];        // array of user IDs (or user objects if populated)
    admin: string;            // user ID (or user object) for the group admin
    totalSales: number;
    avgOrderValue: number;
    activeOrders: number;
    createdAt: string;        // or Date, if you're consistently parsing these
    updatedAt: string;        // or Date
  }
  