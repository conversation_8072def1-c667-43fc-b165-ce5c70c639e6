'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useCheckout } from '@/lib/redux/hooks/useCheckout'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { formatCurrency } from '@/lib/utils'
import { ArrowLeft, ArrowRight, CreditCard, BanknoteIcon, Landmark, Check, CheckCircle2 } from 'lucide-react'
import { GroupOrderReduxSummary } from '@/components/group-orders/GroupOrderReduxSummary'
import { PaymentMethodSelector } from '@/components/payments/PaymentMethodSelector'
import { PaymentForm } from '@/components/payments/PaymentForm'
import { PaymentMethodType, PaymentFormData } from '@/types/payment'

// Define form schemas for each step
const customerDetailsSchema = z.object({
  name: z.string().min(2, { message: 'Name is required' }),
  email: z.string().email({ message: 'Valid email is required' }),
  address: z.string().min(5, { message: 'Address is required' }),
  city: z.string().min(2, { message: 'City is required' }),
  country: z.string().min(2, { message: 'Country is required' }),
  postalCode: z.string().min(1, { message: 'Postal code is required' }),
})

const paymentSchema = z.object({
  paymentMethod: z.enum(['credit_card', 'bank_transfer', 'eft']),
  savePaymentInfo: z.boolean().optional(),
})

interface GroupOrderReduxCheckoutProps {
  groupId: string
}

// Component for the group order checkout process
export function GroupOrderReduxCheckout({ groupId }: GroupOrderReduxCheckoutProps) {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType>('credit_card')

  // Use the checkout hook
  const {
    discountInfo,
    formData,
    updateFormData,
    submitOrder,
    processPayment,
    subtotal
  } = useCheckout(groupId)

  // React Hook Form setup for customer details
  const customerDetailsForm = useForm<z.infer<typeof customerDetailsSchema>>({
    resolver: zodResolver(customerDetailsSchema),
    defaultValues: {
      name: formData.name || '',
      email: formData.email || '',
      address: formData.address || '',
      city: formData.city || '',
      country: formData.country || '',
      postalCode: formData.postalCode || '',
    }
  })

  // React Hook Form setup for payment
  const paymentForm = useForm<z.infer<typeof paymentSchema>>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: formData.paymentMethod || 'credit_card',
      savePaymentInfo: formData.savePaymentInfo || false,
    }
  })

  // Handle form submissions for each step
  const handleCustomerDetailsSubmit = (data: z.infer<typeof customerDetailsSchema>) => {
    updateFormData(data)
    setCurrentStep(1)
  }

  const handlePaymentMethodSelect = (method: PaymentMethodType) => {
    setSelectedPaymentMethod(method)
    updateFormData({ paymentMethod: method })
  }

  const handlePaymentFormSubmit = async (paymentFormData: PaymentFormData) => {
    updateFormData(paymentFormData)
    setCurrentStep(2)
  }

  // Submit the complete order with payment processing
  const handleOrderSubmit = async () => {
    setIsSubmitting(true)
    try {
      // Process payment with the collected payment data
      const paymentFormData: PaymentFormData = {
        paymentMethod: selectedPaymentMethod,
        savePaymentInfo: formData.savePaymentInfo || false,
        cardNumber: formData.cardNumber,
        expiryMonth: formData.expiryMonth,
        expiryYear: formData.expiryYear,
        cvv: formData.cvv,
        cardholderName: formData.cardholderName,
        bankName: formData.bankName,
        accountNumber: formData.accountNumber,
        accountHolderName: formData.accountHolderName,
        branchCode: formData.branchCode
      }

      const paymentResult = await processPayment(paymentFormData)

      if (paymentResult.success) {
        router.push(`/groups/${groupId}/orders?success=true&orderId=${paymentResult.orderId}`)
      } else {
        console.error('Payment failed:', paymentResult.error)
        // Handle payment failure - show error message
      }
    } catch (error) {
      console.error('Error submitting order:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Animation variants for step transitions
  const variants = {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  }

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 0: // Customer details
        return (
          <motion.div
            key="customer-details"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
              <CardDescription>
                Please provide your shipping and contact details
              </CardDescription>
            </CardHeader>
            <form onSubmit={customerDetailsForm.handleSubmit(handleCustomerDetailsSubmit)}>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      {...customerDetailsForm.register('name')}
                    />
                    {customerDetailsForm.formState.errors.name && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      {...customerDetailsForm.register('email')}
                    />
                    {customerDetailsForm.formState.errors.email && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.email.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Input
                    id="address"
                    {...customerDetailsForm.register('address')}
                  />
                  {customerDetailsForm.formState.errors.address && (
                    <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.address.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      {...customerDetailsForm.register('city')}
                    />
                    {customerDetailsForm.formState.errors.city && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.city.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Input
                      id="postalCode"
                      {...customerDetailsForm.register('postalCode')}
                    />
                    {customerDetailsForm.formState.errors.postalCode && (
                      <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.postalCode.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    {...customerDetailsForm.register('country')}
                  />
                  {customerDetailsForm.formState.errors.country && (
                    <p className="text-sm text-red-500">{customerDetailsForm.formState.errors.country.message}</p>
                  )}
                </div>
              </CardContent>

              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/groups/${groupId}/products`)}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Products
                </Button>
                <Button type="submit">
                  Continue to Payment <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </form>
          </motion.div>
        );

      case 1: // Payment method
        return (
          <motion.div
            key="payment"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Payment</CardTitle>
              <CardDescription>
                Choose your payment method and enter payment details
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Payment Method Selection */}
              <PaymentMethodSelector
                selectedMethod={selectedPaymentMethod}
                onMethodChange={handlePaymentMethodSelect}
                amount={subtotal}
                currency="ZAR"
                disabled={isSubmitting}
              />

              {/* Payment Form */}
              <PaymentForm
                paymentMethod={selectedPaymentMethod}
                onSubmit={handlePaymentFormSubmit}
                isLoading={isSubmitting}
                disabled={isSubmitting}
              />

              {/* Discount Information */}
              {discountInfo && discountInfo.discountPercentage > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
                  <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                  <div>
                    <p className="font-medium text-green-800">
                      Group discount applied!
                    </p>
                    <p className="text-sm text-green-700">
                      You're saving {formatCurrency(discountInfo.discountAmount)} ({discountInfo.discountPercentage}% off)
                    </p>
                  </div>
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(0)}
                disabled={isSubmitting}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Information
              </Button>
            </CardFooter>
          </motion.div>
        );

      case 2: // Order summary
        return (
          <motion.div
            key="summary"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={variants}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
              <CardDescription>
                Review your order details before confirming
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <GroupOrderReduxSummary
                customerInfo={{
                  name: formData.name || '',
                  email: formData.email || '',
                  address: formData.address || '',
                  city: formData.city || '',
                  country: formData.country || '',
                  postalCode: formData.postalCode || '',
                }}
                paymentMethod={
                  formData.paymentMethod === 'credit_card'
                    ? 'Credit Card'
                    : formData.paymentMethod === 'bank_transfer'
                    ? 'Direct Bank Transfer'
                    : 'Electronic Fund Transfer (EFT)'
                }
                onConfirm={handleOrderSubmit}
                isSubmitting={isSubmitting}
              />
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(1)}
                disabled={isSubmitting}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Payment
              </Button>
              <Button
                onClick={handleOrderSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>Place Order <Check className="ml-2 h-4 w-4" /></>
                )}
              </Button>
            </CardFooter>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <AnimatePresence mode="wait">
        {renderStep()}
      </AnimatePresence>
    </Card>
  );
}
