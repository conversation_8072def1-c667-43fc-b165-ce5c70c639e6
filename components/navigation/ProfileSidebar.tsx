// // components/navigation/ProfileSidebar.tsx

"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  ChevronLeft,
  ChevronRight,
  User,
  ShoppingBag,
  Users,
  CreditCard,
  Settings,
  HelpCircle,
  LogOut,
  List,
  Truck,
  Box,
  TrendingUp,
  ChevronDown,
  Heart,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { useAuth } from "@/context/AuthContext"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { StokvelGroup } from "@/types/stokvelgroup"

const mainNavItems = [
  { icon: User, label: "My Profile", href: "/profile" },
  { icon: ShoppingBag, label: "My Orders", href: "/profile/orders" },
  { icon: Heart, label: "My Wishlist", href: "/profile/wishlist" },
  { icon: Users, label: "My Groups", href: "/profile/groups" },
  { icon: CreditCard, label: "Payment Methods", href: "/profile/payments" },
]

const groupNavItems = [
  { icon: Box, label: "Group Dashboard", href: "/group/:groupId" },
  { icon: List, label: "Product Listings", href: "/group/:groupId/products" },
  { icon: TrendingUp, label: "Purchase Tracking", href: "/group/:groupId/tracking" },
  { icon: Truck, label: "Delivery Info", href: "/group/:groupId/delivery" },
]

const secondaryNavItems = [
  { icon: Settings, label: "Settings", href: "/profile/settings" },
  { icon: HelpCircle, label: "Help", href: "/profile/help" },
]

export function ProfileSidebar() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [selectedGroup, setSelectedGroup] = useState<StokvelGroup | null>(null)
  const pathname = usePathname()
  const router = useRouter()
  const { logout} = useAuth()
  const { userGroups, error } = useGroupMembership()

  useEffect(() => {
    if (userGroups.length > 0 && !selectedGroup) {
      setSelectedGroup(userGroups[0])
    }
  }, [userGroups, selectedGroup])

  const isLinkActive = (href: string) => {
    if (href === "/profile") {
      return pathname === href
    }
    return pathname.startsWith(href.replace(":groupId", selectedGroup?._id || ""))
  }

  const renderNavItem = (item: { icon: React.ElementType; label: string; href: string }) => {
    const isActive = isLinkActive(item.href)
    const href = item.href.replace(":groupId", selectedGroup?._id || "")
    return (
      <Link key={item.label} href={href as `/profile` | `/profile/orders` | `/profile/groups` | `/profile/payments` | `/group/${string}` | `/group/${string}/products` | `/group/${string}/tracking` | `/group/${string}/delivery`}>
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start",
            !isSidebarOpen && "px-2",
            isActive && "bg-[#2A7C6C] text-white hover:bg-[#236358] hover:text-white",
          )}
        >
          <item.icon className="h-5 w-5 mr-2" />
          {isSidebarOpen && <span style={{ fontFamily: "Avenir, sans-serif" }}>{item.label}</span>}
        </Button>
      </Link>
    )
  }

  const handleGroupSelect = (group: StokvelGroup) => {
    setSelectedGroup(group)
    router.push(`/group/${group._id}`)
  }


  // const handleGroupSelect = (group: StokvelGroup) => {
  //   setSelectedGroup(group)
  //   router.push(`/group/${group._id}`)
  // }

  const navigateToHome = () => {
    router.push("/")
  }

  return (
    <aside
      className={cn(
        "relative flex flex-col",
        "min-h-screen bg-white border-r transition-all duration-300 ease-in-out",
        "shadow-[1px_0_3px_rgba(0,0,0,0.1)]",
        isSidebarOpen ? "w-64" : "w-20",
      )}
    >
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-4 top-6 z-50 h-8 w-8 rounded-full border bg-white shadow-md"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
      >
        {isSidebarOpen ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
      </Button>

      {/* Logo and Profile Section */}
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6 cursor-pointer" onClick={navigateToHome}>
          <div className="relative h-10 w-10 overflow-hidden rounded-full">
            <Image src="/StokvelLogo.avif" alt="Stokvel Logo" fill className="object-cover" />
          </div>
          {isSidebarOpen && (
            <div>
              <h2
                className="text-lg font-semibold text-[#2A7C6C]"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.02em",
                }}
              >
                My Stokvel
              </h2>
            </div>
          )}
        </div>
        {isSidebarOpen && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-between bg-blue-50 hover:bg-blue-100 border-blue-200 text-[#2A7C6C]"
              >
                {selectedGroup ? selectedGroup.name : "Select Group"}
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-white border-blue-200">
              {userGroups.map((group) => (
                <DropdownMenuItem
                  key={group._id}
                  onSelect={() => handleGroupSelect(group)}
                  className="hover:bg-[#2A7C6C]/10 focus:bg-[#2A7C6C]/20 cursor-pointer"
                >
                  <div className="flex items-center justify-between w-full">
                    <span>{group.name}</span>
                    <span className="text-xs text-gray-500 opacity-70">Member</span>
                  </div>
                </DropdownMenuItem>
              ))}
              {userGroups.length === 0 && (
                <DropdownMenuItem
                  disabled
                  className="text-gray-400 italic"
                >
                  No groups available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {error && (
        <div className="px-4 py-2 bg-red-100 text-red-700 text-sm">Failed to load groups. Please try again.</div>
      )}

      {/* Main Navigation */}
      <nav className="flex-1 px-4 overflow-y-auto">
        <div className="space-y-1">{mainNavItems.map(renderNavItem)}</div>

        {selectedGroup && (
          <>
            <Separator className="my-4" />
            <div className="space-y-1">{groupNavItems.map(renderNavItem)}</div>
          </>
        )}

        <Separator className="my-4" />

        {/* Secondary Navigation */}
        <div className="space-y-1">{secondaryNavItems.map(renderNavItem)}</div>
      </nav>

      {/* Logout Section */}
      <div className="p-4 mt-auto border-t">
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
            !isSidebarOpen && "px-2",
          )}
          onClick={logout}
        >
          <LogOut className="h-5 w-5 mr-2" />
          {isSidebarOpen && <span style={{ fontFamily: "Avenir, sans-serif" }}>Logout</span>}
        </Button>
      </div>
    </aside>
  )
}
