"use client"

import React, { useMemo, useCallback } from "react"
import { useMultiStepForm } from "@/hooks/useMultiStepForm"
import { StepSelector } from "./StepSelector"
import { PricingPlans } from "./PricingPlans"
import { UserRegistrationStep } from "./UserRegistrationStep"
import { FormSummary } from "./FormSummary"
import type { FormData, Beneficiary } from "./StepComponentProps"


interface MultiStepFormProps {
  onClose: () => void
}

export const MultiStepForm: React.FC<MultiStepFormProps> = ({ onClose }) => {
  const initialFormData: FormData = {
    investmentType: "",
    selectedPlan: "",
    userId: "",
    email: "",
    name: "",
    phone: "",
    planDuration: "",
    existingUserId: "",
    planPrice: "",
    planTitle: "",
    joiningFee: "",
    benefits: [],
    beneficiaries: []
  }

  const { currentStepIndex, next, back, formData, updateFormData } = useMultiStepForm<FormData>([], initialFormData)

  const handleFormUpdate = useCallback((key: keyof FormData, value: string | string[] | Beneficiary[]) => {
    updateFormData({ [key]: value })
  }, [updateFormData])

  const steps = useMemo(() => [
    <StepSelector
      key="step-selector"
      formData={initialFormData}
      updateFormData={handleFormUpdate}
      onNext={() => {}}
      onPrev={() => {}}
    />,
    <PricingPlans
      key="pricing-plans"
      formData={initialFormData}
      updateFormData={handleFormUpdate}
      onNext={() => {}}
      onPrev={() => {}}
    />,
    <UserRegistrationStep
      key="user-registration"
      formData={initialFormData}
      updateFormData={handleFormUpdate}
      onNext={() => {}}
      onPrev={() => {}}
    />,
    <FormSummary
      key="form-summary"
      formData={initialFormData}
      onClose={onClose}
      onPrev={() => {}}
    />,
  ], [onClose, handleFormUpdate, initialFormData])

  const handleNext = useCallback(() => {
    const currentStepProps = steps[currentStepIndex].props
    if ('onNext' in currentStepProps && typeof currentStepProps.onNext === 'function') {
      currentStepProps.onNext()
    }
    next()
  }, [currentStepIndex, next, steps])

  const currentStep = useMemo(() => {
    return React.cloneElement(steps[currentStepIndex], {
      formData,
      updateFormData: handleFormUpdate,
      onNext: handleNext,
      onPrev: back,
    })
  }, [currentStepIndex, formData, handleFormUpdate, handleNext, back, steps])

  return (
    <div className="flex flex-col items-center justify-center">
      <h2 className="mb-6 text-2xl font-bold text-[#2F4858]">Start Saving with Stokvel</h2>
      {currentStep}
    </div>
  )
}