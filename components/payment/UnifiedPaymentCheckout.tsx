'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { PaymentMethodSelector, PaymentMethod } from './PaymentMethodSelector';

// Import payment components
import { PayFastCheckout } from '@/modules/payments/payfast';
import { PeachCheckout } from '@/modules/payments/peach';
import { CODCheckout } from '@/modules/payments/cod';

interface UnifiedPaymentCheckoutProps {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  customerEmail: string;
  customerName: string;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
  disabled?: boolean;
  defaultMethod?: PaymentMethod;
}

export function UnifiedPaymentCheckout({
  orderId,
  amount,
  currency = 'ZAR',
  description,
  customerEmail,
  customerName,
  onSuccess,
  onError,
  onCancel,
  className = '',
  disabled = false,
  defaultMethod
}: UnifiedPaymentCheckoutProps) {
  const [step, setStep] = useState<'select' | 'payment' | 'success'>('select');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | undefined>(defaultMethod);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Auto-proceed to payment if default method is provided
  useEffect(() => {
    if (defaultMethod) {
      setSelectedMethod(defaultMethod);
      setStep('payment');
    }
  }, [defaultMethod]);

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setError(null);
  };

  const handleProceedToPayment = () => {
    if (!selectedMethod) {
      setError('Please select a payment method');
      return;
    }
    setStep('payment');
  };

  const handlePaymentSuccess = (result: any) => {
    setPaymentResult(result);
    setStep('success');
    onSuccess?.(result);
  };

  const handlePaymentError = (error: string) => {
    setError(error);
    onError?.(error);
  };

  const handleBackToSelection = () => {
    setStep('select');
    setError(null);
  };

  const renderPaymentComponent = () => {
    if (!selectedMethod) return null;

    const commonProps = {
      orderId,
      amount,
      currency,
      description,
      customerEmail,
      customerName,
      onSuccess: handlePaymentSuccess,
      onError: handlePaymentError,
      onCancel: handleBackToSelection,
      disabled
    };

    switch (selectedMethod) {
      case 'payfast':
        return <PayFastCheckout {...commonProps} />;
      
      case 'peach':
        return <PeachCheckout {...commonProps} />;
      
      case 'cod':
        return <CODCheckout {...commonProps} />;
      
      default:
        return null;
    }
  };

  const getMethodName = (method: PaymentMethod) => {
    switch (method) {
      case 'payfast': return 'PayFast';
      case 'peach': return 'Peach Payments';
      case 'cod': return 'Cash on Delivery';
      default: return 'Unknown';
    }
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <AnimatePresence mode="wait">
        {step === 'select' && (
          <motion.div
            key="method-selection"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <PaymentMethodSelector
              selectedMethod={selectedMethod}
              onMethodSelect={handleMethodSelect}
              amount={amount}
              currency={currency}
            />

            {/* Error Display */}
            {error && (
              <Alert className="mt-4 border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="mt-6 flex gap-3">
              <Button
                onClick={handleProceedToPayment}
                disabled={!selectedMethod || disabled}
                className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                Continue to Payment
              </Button>

              {onCancel && (
                <Button
                  variant="outline"
                  onClick={onCancel}
                  disabled={disabled}
                  className="px-6"
                >
                  Cancel
                </Button>
              )}
            </div>
          </motion.div>
        )}

        {step === 'payment' && selectedMethod && (
          <motion.div
            key="payment-processing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Payment Method Header */}
            <Card className="mb-6 shadow-sm border-0 bg-gradient-to-r from-gray-50 to-white">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleBackToSelection}
                      className="p-2"
                    >
                      <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        Payment via {getMethodName(selectedMethod)}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)} - {description}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Component */}
            {renderPaymentComponent()}

            {/* Error Display */}
            {error && (
              <Alert className="mt-4 border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}
          </motion.div>
        )}

        {step === 'success' && paymentResult && (
          <motion.div
            key="payment-success"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-blue-50">
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Payment Initiated Successfully!
                </h2>
                
                <p className="text-gray-600 mb-4">
                  {selectedMethod === 'cod' 
                    ? 'Your Cash on Delivery order has been placed. You will receive updates about your delivery.'
                    : 'Your payment has been initiated. You may be redirected to complete the payment process.'
                  }
                </p>

                <div className="space-y-2 text-sm text-gray-600 mb-6">
                  <p><strong>Order ID:</strong> {orderId}</p>
                  <p><strong>Amount:</strong> {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}</p>
                  <p><strong>Payment Method:</strong> {getMethodName(selectedMethod!)}</p>
                  {paymentResult.trackingNumber && (
                    <p><strong>Tracking Number:</strong> {paymentResult.trackingNumber}</p>
                  )}
                </div>

                {/* Additional Info Based on Payment Method */}
                {selectedMethod === 'cod' && (
                  <Alert className="border-blue-200 bg-blue-50 text-left">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Next Steps:</strong>
                      <ul className="mt-2 space-y-1 text-sm">
                        <li>• You will receive SMS updates about your delivery</li>
                        <li>• Prepare the exact cash amount for payment</li>
                        <li>• Ensure someone is available at the delivery address</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {(selectedMethod === 'payfast' || selectedMethod === 'peach') && (
                  <Alert className="border-blue-200 bg-blue-50 text-left">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Next Steps:</strong>
                      <ul className="mt-2 space-y-1 text-sm">
                        <li>• Complete the payment on the secure payment page</li>
                        <li>• You will receive a confirmation email once payment is processed</li>
                        <li>• Your order will be processed immediately after payment confirmation</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
