


"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useGroups } from "@/lib/redux/hooks/useGroups"
import { EditStokvelGroupModal } from "@/components/admin/forms/EditStokvelGroupModal"
import { DeleteStokvelGroupDialog } from "@/components/admin/forms/DeleteStokvelGroupDialog"
import { StokvelGroup as FrontendStokvelGroup } from "@/lib/frontendGroupUtilities"
// import { StokvelGroup as ApiStokvelGroup } from "@/types/stokvelgroup"

// Create a combined type that includes all required properties
// interface EnhancedStokvelGroup extends ApiStokvelGroup {
//   hasDelivery: boolean;
// }

export function StokvelGroupsTable() {
  const { allGroups, isLoading, error } = useGroups()
  const [editingGroup, setEditingGroup] = useState<FrontendStokvelGroup | null>(null)
  const [deletingGroup, setDeletingGroup] = useState<FrontendStokvelGroup | null>(null)

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error loading groups</div>

  return (
    <Card>
      <CardHeader>
        <CardTitle
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Stokvel Buying Groups
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead>Location</TableHead>
              <TableHead className="text-right">Members</TableHead>
              <TableHead className="text-right">Total Sales</TableHead>
              <TableHead className="text-right">Avg. Order Value</TableHead>
              <TableHead className="text-right">Active Orders</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {allGroups.map((group) => (
              <TableRow key={group._id}>
                <TableCell className="font-medium">{group.name}</TableCell>
                <TableCell>{group.geolocation}</TableCell>
                <TableCell className="text-right">{group.members?.length || 0}</TableCell>
                <TableCell className="text-right">R {group.totalSales?.toFixed(2) || '0.00'}</TableCell>
                <TableCell className="text-right">R {group.avgOrderValue?.toFixed(2) || '0.00'}</TableCell>
                <TableCell className="text-right">{group.activeOrders || 0}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => {
                        // Convert ApiStokvelGroup to FrontendStokvelGroup
                        const groupWithDelivery: FrontendStokvelGroup = {
                          ...group,
                          hasDelivery: false,
                          createdAt: new Date(group.createdAt),
                          updatedAt: new Date(group.updatedAt)
                        };
                        setEditingGroup(groupWithDelivery);
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Convert ApiStokvelGroup to FrontendStokvelGroup
                        const groupWithDelivery: FrontendStokvelGroup = {
                          ...group,
                          hasDelivery: false,
                          createdAt: new Date(group.createdAt),
                          updatedAt: new Date(group.updatedAt)
                        };
                        setDeletingGroup(groupWithDelivery);
                      }}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      {editingGroup && (
        <EditStokvelGroupModal
          group={editingGroup}
          onClose={() => setEditingGroup(null)}
        />
      )}
      {deletingGroup && (
        <DeleteStokvelGroupDialog
          group={deletingGroup}
          onClose={() => setDeletingGroup(null)}
        />
      )}
    </Card>
  )
}

