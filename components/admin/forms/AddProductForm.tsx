// "use client"

// import { useState } from "react"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog"

// export function AddProductForm() {
//   const [isOpen, setIsOpen] = useState(false)

//   const handleSubmit = (event: React.FormEvent) => {
//     event.preventDefault()
//     // Handle form submission logic here
//     setIsOpen(false)
//   }

//   return (
//     <Dialog open={isOpen} onOpenChange={setIsOpen}>
//       <DialogTrigger asChild>
//         <Button className="bg-[#2A7C6C] hover:bg-[#236358]" style={{ fontFamily: "Avenir, sans-serif" }}>
//           Add Product
//         </Button>
//       </DialogTrigger>
//       <DialogContent className="sm:max-w-[425px]">
//         <DialogHeader>
//           <DialogTitle style={{
//             fontFamily: "ClashDisplay-Variable, sans-serif",
//             letterSpacing: "-0.02em",
//           }}>Add New Product</DialogTitle>
//         </DialogHeader>
//         <form onSubmit={handleSubmit} className="space-y-4">
//           <Input placeholder="Product Name" style={{ fontFamily: "Avenir, sans-serif" }} />
//           <Input placeholder="Price" type="number" style={{ fontFamily: "Avenir, sans-serif" }} />
//           <Input placeholder="Description" style={{ fontFamily: "Avenir, sans-serif" }} />
//           <Input placeholder="Stock Quantity" type="number" style={{ fontFamily: "Avenir, sans-serif" }} />
//           <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]" style={{ fontFamily: "Avenir, sans-serif" }}>
//             Add Product
//           </Button>
//         </form>
//       </DialogContent>
//     </Dialog>
//   )
// }




// "use client"

// import React, { useState } from "react"
// import { useForm } from "react-hook-form"
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Textarea } from "@/components/ui/textarea"
// import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import { useProductCategories } from "@/context/ProductCategoriesProvider"
// import { useCreateProduct } from "@/lib/frontendProductUtilities"

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5000000, `Max image size is 5MB.`)
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported.",
//     ),
// })

// type ProductFormType = z.infer<typeof productSchema>

// export function AddProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const createProduct = useCreateProduct()
//   const { categories } = useProductCategories()
//   const [submitting, setSubmitting] = useState(false)
//   const [success, setSuccess] = useState(false)

//   const form = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   })

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       setSubmitting(true)
//       const formData = new FormData()
//       formData.append("name", data.name)
//       formData.append("description", data.description)
//       formData.append("price", data.price.toString())
//       formData.append("category", data.category)
//       formData.append("stock", data.stock.toString())
//       formData.append("image", data.image)

//       await createProduct.mutateAsync(formData)
//       setSuccess(true)
//       form.reset()
//       setTimeout(() => {
//         onSuccess?.()
//       }, 3000)
//     } catch (error) {
//       console.error("Failed to create Product", error)
//       setSuccess(false)
//     } finally {
//       setSubmitting(false)
//     }
//   }

//   if (success) {
//     return (
//       <div className="p-4">
//         <h2 className="text-xl font-semibold mb-2">Product Created!</h2>
//         <p className="text-sm text-gray-700">
//           Your new Product was created successfully. This window will close in 3 seconds...
//         </p>
//       </div>
//     )
//   }

//   return (
//     <Form {...form}>
//       <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
//         <FormField
//           control={form.control}
//           name="name"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Product Name</FormLabel>
//               <FormControl>
//                 <Input placeholder="Enter product name" {...field} />
//               </FormControl>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <FormField
//           control={form.control}
//           name="description"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Description</FormLabel>
//               <FormControl>
//                 <Textarea placeholder="Enter product description" {...field} />
//               </FormControl>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <FormField
//           control={form.control}
//           name="price"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Price</FormLabel>
//               <FormControl>
//                 <Input
//                   type="number"
//                   placeholder="Enter price"
//                   {...field}
//                   onChange={(e) => field.onChange(Number.parseFloat(e.target.value))}
//                 />
//               </FormControl>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <FormField
//           control={form.control}
//           name="category"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Category</FormLabel>
//               <Select onValueChange={field.onChange} defaultValue={field.value}>
//                 <FormControl>
//                   <SelectTrigger>
//                     <SelectValue placeholder="Select a category" />
//                   </SelectTrigger>
//                 </FormControl>
//                 <SelectContent>
//                   {categories.map((category) => (
//                     <SelectItem key={category._id} value={category._id}>
//                       {category.name}
//                     </SelectItem>
//                   ))}
//                 </SelectContent>
//               </Select>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <FormField
//           control={form.control}
//           name="stock"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Stock</FormLabel>
//               <FormControl>
//                 <Input
//                   type="number"
//                   placeholder="Enter stock quantity"
//                   {...field}
//                   onChange={(e) => field.onChange(Number.parseInt(e.target.value, 10))}
//                 />
//               </FormControl>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <FormField
//           control={form.control}
//           name="image"
//           render={({ field }) => (
//             <FormItem>
//               <FormLabel>Product Image</FormLabel>
//               <FormControl>
//                 <Input
//                   type="file"
//                   accept="image/jpeg,image/png,image/webp"
//                   onChange={(e) => field.onChange(e.target.files?.[0])}
//                 />
//               </FormControl>
//               <FormMessage />
//             </FormItem>
//           )}
//         />

//         <div className="flex justify-end">
//           <Button type="submit" disabled={submitting}>
//             {submitting ? "Saving..." : "Create Product"}
//           </Button>
//         </div>
//       </form>
//     </Form>
//   )
// }




"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { useCreateProduct } from "@/lib/frontendProductUtilities";

/**
 * 1) Conditionally define the "image" field so it doesn't reference
 *    File in SSR.
 *    - On the server (build time), use z.any()
 *    - In the browser, use z.instanceof(File)
 */
const fileField = typeof window === "undefined"
  ? z.any()
  : z.instanceof(File)
      .refine((file) => file.size <= 5_000_000, "Max image size is 5MB.")
      .refine(
        (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
        "Only .jpg, .png, and .webp formats are supported."
      );

/**
 * 2) Build the rest of the product schema with the conditional fileField
 */
const productSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  price: z.number().positive("Price must be a positive number."),
  category: z.string().nonempty("Category is required."),
  stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
  image: fileField,
});

type ProductFormType = z.infer<typeof productSchema>;

export function AddProductForm({ onSuccess }: { onSuccess?: () => void }) {
  // 3) Use your standard useForm, but with the updated productSchema
  const form = useForm<ProductFormType>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      category: "",
      stock: 0,
      // The "image" field defaults to undefined until the user selects a file
    },
  });

  const { data: categories = [] } = useGetCategoriesQuery();
  const createProduct = useCreateProduct();
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const onSubmit = async (data: ProductFormType) => {
    try {
      setSubmitting(true);

      // Build FormData to upload file + fields
      const formData = new FormData();
      formData.append("name", data.name);
      formData.append("description", data.description);
      formData.append("price", data.price.toString());
      formData.append("category", data.category);
      formData.append("stock", data.stock.toString());
      formData.append("image", data.image as File); // safely cast to File

      await createProduct.mutateAsync(formData);
      setSuccess(true);
      form.reset();

      setTimeout(() => {
        onSuccess?.();
      }, 3000);
    } catch (error) {
      console.error("Failed to create Product", error);
      setSuccess(false);
    } finally {
      setSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="p-4">
        <h2 className="text-xl font-semibold mb-2">Product Created!</h2>
        <p className="text-sm text-gray-700">
          Your new Product was created successfully. This window will close in 3
          seconds...
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Product Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter product name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter product description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Price */}
        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Price</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter price"
                  {...field}
                  onChange={(e) => field.onChange(Number.parseFloat(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Category */}
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category._id} value={category._id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Stock */}
        <FormField
          control={form.control}
          name="stock"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Stock</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter stock quantity"
                  {...field}
                  onChange={(e) => field.onChange(Number.parseInt(e.target.value, 10))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Image (File Upload) */}
        <FormField
          control={form.control}
          name="image"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Product Image</FormLabel>
              <FormControl>
                <Input
                  type="file"
                  accept="image/jpeg,image/png,image/webp"
                  onChange={(e) => field.onChange(e.target.files?.[0])}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={submitting}>
            {submitting ? "Saving..." : "Create Product"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
