"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  Gift, 
  TrendingUp, 
  Share2,
  Copy,
  DollarSign,
  Award,
  Target,
  Clock,
  Crown,
  Zap,
  BarChart3,
  Globe,
  Sparkles,
  Trophy,
  Rocket,
  Brain,
  Calendar,
  Star
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import { ReferralAnalyticsDashboard } from "./ReferralAnalyticsDashboard";
import { SmartRecommendations } from "./SmartRecommendations";
import { ReferralLeaderboard } from "./ReferralLeaderboard";
import { AutomatedCampaigns } from "./AutomatedCampaigns";
import { PremiumSocialShare } from "./PremiumSocialShare";
import { AdvancedRedemption } from "./AdvancedRedemption";

interface PremiumReferralDashboardProps {
  userId: string;
}

interface PremiumUserData {
  referralCode: string;
  name: string;
  tier: string;
  tierProgress: number;
  nextTierRequirement: number;
  lifetimeEarnings: number;
  currentStreak: number;
  badges: string[];
  isPremium: boolean;
}

interface PremiumStats {
  totalReferrals: number;
  activeReferrals: number;
  conversionRate: number;
  averageOrderValue: number;
  totalEarnings: number;
  monthlyGrowth: number;
  clickThroughRate: number;
  geographicReach: number;
  topPerformingPlatform: string;
  predictedMonthlyEarnings: number;
}

export function PremiumReferralDashboard({ userId }: PremiumReferralDashboardProps) {
  const [userData, setUserData] = useState<PremiumUserData | null>(null);
  const [stats, setStats] = useState<PremiumStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (userId) {
      fetchPremiumData();
    }
  }, [userId]);

  const fetchPremiumData = async () => {
    try {
      // Simulate premium data loading
      setTimeout(() => {
        setUserData({
          referralCode: 'PREMIUM123',
          name: 'Premium User',
          tier: 'Diamond',
          tierProgress: 75,
          nextTierRequirement: 100,
          lifetimeEarnings: 15420.50,
          currentStreak: 12,
          badges: ['Top Performer', 'Social Media Master', 'Conversion King'],
          isPremium: true
        });

        setStats({
          totalReferrals: 247,
          activeReferrals: 189,
          conversionRate: 23.5,
          averageOrderValue: 156.80,
          totalEarnings: 15420.50,
          monthlyGrowth: 18.2,
          clickThroughRate: 8.7,
          geographicReach: 12,
          topPerformingPlatform: 'WhatsApp',
          predictedMonthlyEarnings: 2340.00
        });

        setIsLoading(false);
        toast.success('Premium dashboard loaded successfully!');
      }, 1500);
    } catch (error) {
      console.error('Error loading premium data:', error);
      setIsLoading(false);
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Diamond': return 'from-purple-500 to-pink-500';
      case 'Platinum': return 'from-gray-400 to-gray-600';
      case 'Gold': return 'from-yellow-400 to-yellow-600';
      case 'Silver': return 'from-gray-300 to-gray-500';
      default: return 'from-amber-400 to-amber-600';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Diamond': return Crown;
      case 'Platinum': return Trophy;
      case 'Gold': return Star;
      default: return Award;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="relative">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
          <Sparkles className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 text-purple-600" />
        </div>
        <p className="ml-4 text-gray-600 font-medium">Loading Premium Dashboard...</p>
      </div>
    );
  }

  const TierIcon = getTierIcon(userData?.tier || 'Bronze');

  return (
    <div className="space-y-8">
      {/* Premium Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 p-8 text-white"
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className={`p-3 rounded-full bg-gradient-to-r ${getTierColor(userData?.tier || 'Bronze')}`}>
                  <TierIcon className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Premium Referral Hub</h1>
                  <p className="text-purple-100">Advanced analytics & AI-powered insights</p>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  {userData?.tier} Tier
                </Badge>
                {userData?.isPremium && (
                  <Badge className="bg-yellow-500/20 text-yellow-200 border-yellow-400/30">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                )}
              </div>
              <p className="text-2xl font-bold">R{userData?.lifetimeEarnings.toLocaleString()}</p>
              <p className="text-purple-200 text-sm">Lifetime Earnings</p>
            </div>
          </div>

          {/* Tier Progress */}
          <div className="mt-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Progress to Next Tier</span>
              <span className="text-sm">{userData?.tierProgress}% Complete</span>
            </div>
            <Progress 
              value={userData?.tierProgress || 0} 
              className="h-2 bg-white/20"
            />
            <p className="text-xs text-purple-200 mt-1">
              {userData?.nextTierRequirement - (userData?.tierProgress || 0)} more referrals needed
            </p>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <Sparkles className="h-16 w-16" />
        </div>
        <div className="absolute bottom-4 left-4 opacity-10">
          <Rocket className="h-20 w-20" />
        </div>
      </motion.div>

      {/* Premium Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: "Active Referrals",
            value: stats?.activeReferrals || 0,
            change: "+12%",
            icon: Users,
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          },
          {
            title: "Conversion Rate",
            value: `${stats?.conversionRate || 0}%`,
            change: "+5.2%",
            icon: Target,
            color: "text-green-600",
            bgColor: "bg-green-50"
          },
          {
            title: "Avg Order Value",
            value: `R${stats?.averageOrderValue || 0}`,
            change: "+8.1%",
            icon: DollarSign,
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          },
          {
            title: "Monthly Growth",
            value: `${stats?.monthlyGrowth || 0}%`,
            change: "+15.3%",
            icon: TrendingUp,
            color: "text-orange-600",
            bgColor: "bg-orange-50"
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-md">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-green-600 font-medium">{stat.change} vs last month</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Achievement Badges */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-600" />
            Achievement Badges
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {userData?.badges.map((badge, index) => (
              <motion.div
                key={badge}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full text-sm font-medium shadow-lg"
              >
                <Award className="h-4 w-4" />
                {badge}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Premium Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 bg-gray-100 p-1 rounded-xl">
          <TabsTrigger value="overview" className="rounded-lg">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="ai-insights" className="rounded-lg">
            <Brain className="h-4 w-4 mr-2" />
            AI Insights
          </TabsTrigger>
          <TabsTrigger value="campaigns" className="rounded-lg">
            <Rocket className="h-4 w-4 mr-2" />
            Campaigns
          </TabsTrigger>
          <TabsTrigger value="leaderboard" className="rounded-lg">
            <Trophy className="h-4 w-4 mr-2" />
            Leaderboard
          </TabsTrigger>
          <TabsTrigger value="share" className="rounded-lg">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </TabsTrigger>
          <TabsTrigger value="rewards" className="rounded-lg">
            <Gift className="h-4 w-4 mr-2" />
            Rewards
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <ReferralAnalyticsDashboard userId={userId} />
        </TabsContent>

        <TabsContent value="ai-insights" className="space-y-6">
          <SmartRecommendations userId={userId} />
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <AutomatedCampaigns userId={userId} />
        </TabsContent>

        <TabsContent value="leaderboard" className="space-y-6">
          <ReferralLeaderboard userId={userId} />
        </TabsContent>

        <TabsContent value="share" className="space-y-6">
          <PremiumSocialShare 
            referralCode={userData?.referralCode || ''}
            userName={userData?.name || ''}
            tier={userData?.tier || 'Bronze'}
          />
        </TabsContent>

        <TabsContent value="rewards" className="space-y-6">
          <AdvancedRedemption userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
