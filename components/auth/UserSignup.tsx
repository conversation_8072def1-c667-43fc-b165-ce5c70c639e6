// components/auth/UserSignup.tsx

'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  User,
  Mail,
  Phone,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  Loader2,
  ArrowRight,
  Sparkles,
  Shield
} from 'lucide-react';
import SuccessMessage from './SuccessMessage';

const UserSignup: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const { signup } = useAuth();
  const router = useRouter();

  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    setPasswordStrength(checkPasswordStrength(value));
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !email || !phone || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setError(null);
    setLoading(true);
    try {
      await signup(name, email, phone, password);
      setSuccess(true);
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    } catch (err) {
      console.error('Error during signup:', err);
      setError(err instanceof Error ? err.message : 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return <SuccessMessage message="Sign up successful! Redirecting to login..." />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 relative overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpath d='M50 50L25 75L75 75z'/%3E%3Cpath d='M50 50L75 25L75 75z'/%3E%3Cpath d='M50 50L25 25L75 25z'/%3E%3Cpath d='M50 50L25 25L25 75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      <div className="flex flex-col items-center justify-center min-h-screen px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="inline-block"
            >
              <div className="h-16 w-16 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <User className="h-8 w-8 text-white" />
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent mb-2"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif', letterSpacing: '-0.02em' }}
            >
              Join Stokvel
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-gray-600 text-lg"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Start your community shopping journey
            </motion.p>
          </div>

          {/* Form Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-xl"
          >
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl mb-6"
                >
                  <div className="flex items-center">
                    <div className="h-5 w-5 text-red-500 mr-2">⚠️</div>
                    <span className="text-sm font-medium">{error}</span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            <form onSubmit={handleSignup} className="space-y-6">
              {/* Name Field */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
              >
                <label
                  htmlFor="name"
                  className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  <User className="h-4 w-4 mr-2 text-[#2A7C6C]" />
                  Full Name
                </label>
                <div className="relative">
                  <Input
                    id="name"
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter your full name"
                    required
                    disabled={loading}
                    className="w-full h-12 pl-12 pr-4 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  />
                  <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </motion.div>

              {/* Email Field */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.6 }}
              >
                <label
                  htmlFor="email"
                  className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  <Mail className="h-4 w-4 mr-2 text-[#2A7C6C]" />
                  Email Address
                </label>
                <div className="relative">
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    disabled={loading}
                    className="w-full h-12 pl-12 pr-4 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  />
                  <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </motion.div>

              {/* Phone Field */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.7 }}
              >
                <label
                  htmlFor="phone"
                  className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  <Phone className="h-4 w-4 mr-2 text-[#2A7C6C]" />
                  Phone Number
                </label>
                <div className="relative">
                  <Input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="+27 XX XXX XXXX"
                    required
                    disabled={loading}
                    className="w-full h-12 pl-12 pr-4 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  />
                  <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                </div>
              </motion.div>
              {/* Password Field */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.8 }}
              >
                <label
                  htmlFor="password"
                  className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  <Lock className="h-4 w-4 mr-2 text-[#2A7C6C]" />
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => handlePasswordChange(e.target.value)}
                    placeholder="Create a strong password"
                    required
                    disabled={loading}
                    className="w-full h-12 pl-12 pr-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  />
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {/* Password Strength Indicator */}
                {password && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-3"
                  >
                    <div className="flex space-x-1 mb-2">
                      {[1, 2, 3, 4, 5].map((level) => (
                        <div
                          key={level}
                          className={`h-2 flex-1 rounded-full transition-colors duration-300 ${
                            level <= passwordStrength
                              ? level <= 2 ? 'bg-red-400' : level <= 3 ? 'bg-yellow-400' : level <= 4 ? 'bg-blue-400' : 'bg-green-400'
                              : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-xs text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                      {passwordStrength <= 2 && 'Weak password'}
                      {passwordStrength === 3 && 'Fair password'}
                      {passwordStrength === 4 && 'Good password'}
                      {passwordStrength === 5 && 'Strong password'}
                    </p>
                  </motion.div>
                )}
              </motion.div>

              {/* Confirm Password Field */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.9 }}
              >
                <label
                  htmlFor="confirmPassword"
                  className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  <Shield className="h-4 w-4 mr-2 text-[#2A7C6C]" />
                  Confirm Password
                </label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    required
                    disabled={loading}
                    className="w-full h-12 pl-12 pr-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  />
                  <Shield className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {/* Password Match Indicator */}
                {confirmPassword && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-2"
                  >
                    <div className={`flex items-center text-xs ${
                      password === confirmPassword ? 'text-green-600' : 'text-red-600'
                    }`} style={{ fontFamily: 'Avenir, sans-serif' }}>
                      {password === confirmPassword ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Passwords match
                        </>
                      ) : (
                        <>
                          <div className="h-3 w-3 mr-1">⚠️</div>
                          Passwords don't match
                        </>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
              {/* Submit Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 1.0 }}
                className="pt-4"
              >
                <Button
                  type="submit"
                  disabled={loading || password !== confirmPassword || passwordStrength < 3}
                  className="w-full h-14 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#236358] hover:to-[#164239] text-white rounded-2xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-5 w-5 mr-2" />
                      Create Account
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </>
                  )}
                </Button>
              </motion.div>

              {/* Terms Notice */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4, delay: 1.1 }}
                className="text-center pt-4"
              >
                <p
                  className="text-xs text-gray-500"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  By creating an account, you agree to our{' '}
                  <Link href="/terms" className="text-[#2A7C6C] hover:underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-[#2A7C6C] hover:underline">
                    Privacy Policy
                  </Link>
                </p>
              </motion.div>
            </form>
          </motion.div>

          {/* Login Link */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="mt-8 text-center"
          >
            <div className="bg-white/50 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50">
              <p
                className="text-gray-600 mb-3"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                Already have an account?
              </p>
              <Link href="/login">
                <Button
                  variant="outline"
                  className="border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white rounded-2xl px-8 py-3 font-semibold transition-all duration-300"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  Sign In Instead
                </Button>
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default UserSignup;


