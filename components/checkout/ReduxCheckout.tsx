'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useCheckout } from '@/lib/redux/hooks/useCheckout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { Stepper } from '@/components/checkout/Stepper'
import { ArrowLeft, ArrowRight, CreditCard, BanknoteIcon, Landmark } from 'lucide-react'

interface ReduxCheckoutProps {
  groupId: string;
}

export function ReduxCheckout({ groupId }: ReduxCheckoutProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    cartItems,
    subtotal,
    formData,
    checkoutStep,
    updateFormData,
    goToStep,
    submitOrder
  } = useCheckout(groupId);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const success = await submitOrder();
      if (success) {
        router.push(`/groups/${groupId}/orders?success=true`);
      }
    } catch (error) {
      console.error('Failed to submit order:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render different steps based on current step
  const renderStep = () => {
    switch (checkoutStep) {
      case 'cart':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold">Your Cart</h2>
            {cartItems.length === 0 ? (
              <div className="text-center py-10">
                <p className="text-gray-500">Your cart is empty</p>
                <Button
                  className="mt-4"
                  onClick={() => router.push(`/groups/${groupId}/products`)}
                >
                  Continue Shopping
                </Button>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {cartItems.map(item => (
                    <div key={item._id} className="flex justify-between items-center border-b pb-4">
                      <div className="flex items-center gap-4">
                        <div className="h-16 w-16 bg-gray-100 rounded-md overflow-hidden">
                          <img
                            src={item.image ? `/api/images/${item.image}` : "/placeholder.svg"}
                            alt={item.name || 'Product'}
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/placeholder.svg";
                            }}
                          />
                        </div>
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-gray-500">Qty: {item.quantity}</p>
                        </div>
                      </div>
                      <p className="font-medium">R {item.subtotal.toFixed(2)}</p>
                    </div>
                  ))}
                </div>

                <div className="flex justify-between items-center pt-4">
                  <span className="text-lg font-medium">Subtotal:</span>
                  <span className="text-xl font-bold">R {subtotal.toFixed(2)}</span>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => goToStep('customer-info')}
                    className="bg-[#2A7C6C] hover:bg-[#236358]"
                  >
                    Proceed to Checkout <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </>
            )}
          </div>
        );

      case 'customer-info':
        return (
          <form className="space-y-6" onSubmit={(e) => {
            e.preventDefault();
            goToStep('payment');
          }}>
            <h2 className="text-2xl font-semibold">Customer Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData({ name: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData({ email: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Street Address</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => updateFormData({ address: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => updateFormData({ city: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  value={formData.postalCode}
                  onChange={(e) => updateFormData({ postalCode: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => updateFormData({ country: e.target.value })}
                  required
                />
              </div>
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => goToStep('cart')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Cart
              </Button>

              <Button
                type="submit"
                className="bg-[#2A7C6C] hover:bg-[#236358]"
              >
                Continue to Payment <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        );

      case 'payment':
        return (
          <form className="space-y-6" onSubmit={(e) => {
            e.preventDefault();
            goToStep('summary');
          }}>
            <h2 className="text-2xl font-semibold">Payment Method</h2>

            <RadioGroup
              value={formData.paymentMethod}
              onValueChange={(value) => updateFormData({
                paymentMethod: value as 'credit_card' | 'bank_transfer' | 'eft'
              })}
              className="space-y-4"
            >
              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="credit_card" id="credit_card" />
                <Label htmlFor="credit_card" className="flex items-center">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Credit Card
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                <Label htmlFor="bank_transfer" className="flex items-center">
                  <BanknoteIcon className="mr-2 h-4 w-4" />
                  Direct Bank Transfer
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="eft" id="eft" />
                <Label htmlFor="eft" className="flex items-center">
                  <Landmark className="mr-2 h-4 w-4" />
                  Electronic Fund Transfer (EFT)
                </Label>
              </div>
            </RadioGroup>

            <div className="flex items-center space-x-2 pt-4">
              <Checkbox
                id="save_payment"
                checked={formData.savePaymentInfo}
                onCheckedChange={(checked) =>
                  updateFormData({ savePaymentInfo: checked as boolean })
                }
              />
              <Label htmlFor="save_payment">Save payment information for future orders</Label>
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => goToStep('customer-info')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Customer Info
              </Button>

              <Button
                type="submit"
                className="bg-[#2A7C6C] hover:bg-[#236358]"
              >
                Review Order <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </form>
        );

      case 'summary':
        return (
          <form className="space-y-6" onSubmit={handleSubmit}>
            <h2 className="text-2xl font-semibold">Order Summary</h2>

            <div className="space-y-6">
              <div className="border rounded-md p-4 space-y-4">
                <h3 className="font-medium">Customer Information</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <p className="text-gray-500">Name:</p>
                    <p>{formData.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Email:</p>
                    <p>{formData.email}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-gray-500">Address:</p>
                    <p>{formData.address}, {formData.city}, {formData.postalCode}, {formData.country}</p>
                  </div>
                </div>
              </div>

              <div className="border rounded-md p-4">
                <h3 className="font-medium mb-4">Payment Method</h3>
                <p>
                  {formData.paymentMethod === 'credit_card' && (
                    <span className="flex items-center">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Credit Card
                    </span>
                  )}
                  {formData.paymentMethod === 'bank_transfer' && (
                    <span className="flex items-center">
                      <BanknoteIcon className="mr-2 h-4 w-4" />
                      Direct Bank Transfer
                    </span>
                  )}
                  {formData.paymentMethod === 'eft' && (
                    <span className="flex items-center">
                      <Landmark className="mr-2 h-4 w-4" />
                      Electronic Fund Transfer (EFT)
                    </span>
                  )}
                </p>
              </div>

              <div className="border rounded-md p-4">
                <h3 className="font-medium mb-4">Order Items</h3>
                <div className="space-y-4">
                  {cartItems.map(item => (
                    <div key={item._id} className="flex justify-between items-center border-b pb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{item.quantity}x</span>
                        <span>{item.name}</span>
                      </div>
                      <span>R {item.subtotal.toFixed(2)}</span>
                    </div>
                  ))}

                  <div className="flex justify-between items-center pt-2 font-bold">
                    <span>Total:</span>
                    <span>R {subtotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => goToStep('payment')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Payment
              </Button>

              <Button
                type="submit"
                className="bg-[#2A7C6C] hover:bg-[#236358]"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Processing...' : 'Place Order'}
              </Button>
            </div>
          </form>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-8">
        <Stepper
          steps={['Cart', 'Information', 'Payment', 'Summary']}
          currentStep={
            checkoutStep === 'cart' ? 0 :
            checkoutStep === 'customer-info' ? 1 :
            checkoutStep === 'payment' ? 2 : 3
          }
        />
      </div>

      {renderStep()}
    </div>
  );
}
