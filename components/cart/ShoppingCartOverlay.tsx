"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useCart } from "@/lib/redux/hooks/useCart";
import { useAuth } from "@/context/AuthContext";

interface CartOverlayProps {
  groupId: string;
  onCheckoutComplete: () => void;
}

export function ShoppingCartOverlay({ groupId, onCheckoutComplete }: CartOverlayProps) {
  // We need auth context but don't use the user ID directly
  useAuth();

  // Use Redux cart hooks
  const {
    cartItems,
    updateCartItem,
    removeFromCart,
    totalItems
  } = useCart(groupId);

  const [isOpen, setIsOpen] = useState(false);
  const [localCart, setLocalCart] = useState(cartItems);

  // Synchronize the local cart with Redux cart updates
  useEffect(() => {
    setLocalCart(cartItems);
  }, [cartItems]);

  const handleUpdateQuantity = async (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) return;

    // Optimistic UI update
    setLocalCart((prev) =>
      prev.map((item) => (item._id === productId ? { ...item, quantity: newQuantity } : item))
    );

    // Update in Redux/backend
    await updateCartItem(productId, newQuantity);
  };

  const handleRemoveItem = async (productId: string) => {
    // Optimistic UI update
    setLocalCart((prev) => prev.filter((item) => item._id !== productId));

    // Remove from Redux/backend
    await removeFromCart(productId);
  };

  const handleCheckout = async () => {
    try {
      // Redirect to checkout page
      window.location.href = `/groups/${groupId}/checkout`;
      setIsOpen(false);
      onCheckoutComplete();
    } catch (error) {
      console.error("Error during checkout:", error);
    }
  };

  return (
    <>
      <Button variant="ghost" size="icon" onClick={() => setIsOpen(true)}>
        <span>View Cart ({totalItems})</span>
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Your Shopping Cart</DialogTitle>
          </DialogHeader>

          {localCart.length > 0 ? (
            <div className="space-y-4">
              {localCart.map((item) => (
                <div key={item._id} className="flex items-center justify-between">
                  <span>{item.name}</span>
                  <Input
                    type="number"
                    min={1}
                    value={item.quantity}
                    onChange={(e) => typeof item._id === 'string' && handleUpdateQuantity(item._id, parseInt(e.target.value))}
                  />
                  <Button variant="destructive" onClick={() => typeof item._id === 'string' && handleRemoveItem(item._id)}>
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p>Your cart is empty.</p>
          )}

          <DialogFooter>
            <Button variant="default" onClick={handleCheckout}>
              Proceed to Checkout
            </Button>
            <Button variant="secondary" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
