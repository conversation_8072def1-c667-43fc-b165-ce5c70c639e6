// components/ui/safe-link.tsx
// Type-safe Link wrapper that handles Next.js 15 strict routing

import Link from 'next/link';
import { type ComponentProps } from 'react';

// Define all possible routes in our application
type StaticRoutes = 
  | '/'
  | '/store'
  | '/aboutus'
  | '/faq'
  | '/contact'
  | '/login'
  | '/register'
  | '/profile'
  | '/profile/orders'
  | '/profile/groups'
  | '/profile/payments'
  | '/profile/settings'
  | '/groups'
  | '/admin'
  | '/admin/ai'
  | '/admin/automation'
  | '/admin/analytics'
  | '/admin/performance'
  | '/admin/reports'
  | '/admin/products'
  | '/admin/product-categories'
  | '/admin/product-category-archive'
  | '/admin/product-archive'
  | '/admin/orders'
  | '/admin/customers'
  | '/admin/users/enhanced'
  | '/admin/settings'
  | '/admin/help'
  | '/admin/profile';

type DynamicRoutes = 
  | `/group/${string}`
  | `/group/${string}/products`
  | `/group/${string}/members`
  | `/group/${string}/orders`
  | `/group/${string}/analytics`
  | `/group/${string}/settings`
  | `/group/${string}/tracking`
  | `/group/${string}/delivery`
  | `/group/${string}/new-order`;

type SafeRoute = StaticRoutes | DynamicRoutes;

interface SafeLinkProps extends Omit<ComponentProps<typeof Link>, 'href'> {
  href: SafeRoute | string;
}

/**
 * Type-safe Link component that bypasses Next.js 15 strict routing issues
 * while maintaining type safety for known routes
 */
export function SafeLink({ href, ...props }: SafeLinkProps) {
  // Type assertion to bypass Next.js 15 strict typing while maintaining our own type safety
  return <Link href={href as any} {...props} />;
}

// Export the route types for use in other components
export type { SafeRoute, StaticRoutes, DynamicRoutes };
