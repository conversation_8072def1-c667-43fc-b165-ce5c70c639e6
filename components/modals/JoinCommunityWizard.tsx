// components/modals/JoinCommunityWizard.tsx

"use client";

import { useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { LocationDiscoveryStep } from "./community-steps/LocationDiscoveryStep";
import { GroupSelectionStep } from "./community-steps/GroupSelectionStep";
import { CommunityRegistrationStep } from "./community-steps/CommunityRegistrationStep";
import { useRouter } from "next/navigation";

export type CommunityWizardStep = 
  | "location-discovery"
  | "group-selection" 
  | "registration"
  | "success";

export interface CommunityWizardData {
  location: string;
  selectedGroupId: string;
  selectedGroup?: any;
  availableGroups: any[];
  email?: string;
  name?: string;
  phone?: string;
  password?: string;
}

interface JoinCommunityWizardProps {
  onSuccess?: () => void;
}

const stepTitles: Record<CommunityWizardStep, string> = {
  "location-discovery": "Find Your Community",
  "group-selection": "Choose Your Group",
  "registration": "Join Your Community",
  "success": "Welcome to Your Community!"
};

const stepOrder: CommunityWizardStep[] = [
  "location-discovery",
  "group-selection", 
  "registration"
];

export function JoinCommunityWizard({ onSuccess }: JoinCommunityWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<CommunityWizardStep>("location-discovery");
  const [wizardData, setWizardData] = useState<CommunityWizardData>({
    location: "",
    selectedGroupId: "",
    availableGroups: []
  });
  const [isLoading, setIsLoading] = useState(false);

  const updateWizardData = useCallback((data: Partial<CommunityWizardData>) => {
    setWizardData(prev => ({ ...prev, ...data }));
  }, []);

  const goToStep = (step: CommunityWizardStep) => {
    setCurrentStep(step);
  };

  const getCurrentStepIndex = () => {
    return stepOrder.indexOf(currentStep);
  };

  const getProgressPercentage = () => {
    const currentIndex = getCurrentStepIndex();
    return ((currentIndex + 1) / stepOrder.length) * 100;
  };

  const handleRegistrationSuccess = (user: any) => {
    // Redirect to the group's new-order page for immediate shopping
    if (wizardData.selectedGroupId) {
      router.push(`/groups/${wizardData.selectedGroupId}/new-order`);
    }
    onSuccess?.();
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      x: 0, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300
      }
    },
    exit: { 
      opacity: 0, 
      x: -50, 
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  const renderCurrentStep = () => {
    const commonProps = {
      wizardData,
      updateWizardData,
      goToStep,
      onSuccess: handleRegistrationSuccess,
      setIsLoading
    };

    switch (currentStep) {
      case "location-discovery":
        return <LocationDiscoveryStep {...commonProps} />;
      case "group-selection":
        return <GroupSelectionStep {...commonProps} />;
      case "registration":
        return <CommunityRegistrationStep {...commonProps} />;
      default:
        return <LocationDiscoveryStep {...commonProps} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Bar */}
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <h3 
            className="text-lg font-semibold text-gray-800"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            {stepTitles[currentStep]}
          </h3>
          <span className="text-sm text-gray-500">
            Step {getCurrentStepIndex() + 1} of {stepOrder.length}
          </span>
        </div>
        
        <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${getProgressPercentage()}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[500px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            variants={stepVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="space-y-6"
          >
            {renderCurrentStep()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-3xl"
        >
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-gray-700 font-medium">Processing...</span>
          </div>
        </motion.div>
      )}
    </div>
  );
}
