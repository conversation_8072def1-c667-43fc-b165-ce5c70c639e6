// components/modals/community-steps/GroupSelectionStep.tsx

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Users, MapPin, ArrowRight, ArrowLeft, CheckCircle, Star, TrendingUp, Award } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import type { CommunityWizardData, CommunityWizardStep } from "../JoinCommunityWizard";

interface GroupSelectionStepProps {
  wizardData: CommunityWizardData;
  updateWizardData: (data: Partial<CommunityWizardData>) => void;
  goToStep: (step: CommunityWizardStep) => void;
  onSuccess?: (user: any) => void;
  setIsLoading: (loading: boolean) => void;
}

export function GroupSelectionStep({ 
  wizardData, 
  updateWizardData, 
  goToStep,
  setIsLoading 
}: GroupSelectionStepProps) {
  const [selectedGroupId, setSelectedGroupId] = useState(wizardData.selectedGroupId || "");

  const handleGroupSelect = (group: any) => {
    setSelectedGroupId(group._id);
    updateWizardData({ 
      selectedGroupId: group._id,
      selectedGroup: group 
    });
  };

  const handleContinue = () => {
    if (!selectedGroupId) return;
    goToStep("registration");
  };

  const getGroupBadges = (group: any) => {
    const badges = [];
    
    // Popular badge
    if (group.members?.length >= 20) {
      badges.push({
        icon: TrendingUp,
        text: "Popular",
        className: "bg-gradient-to-r from-orange-400 to-red-500 text-white"
      });
    }
    
    // High savings badge
    if (group.totalSales >= 50000) {
      badges.push({
        icon: Award,
        text: "High Savings",
        className: "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
      });
    }
    
    // Active badge
    if (group.members?.length >= 10) {
      badges.push({
        icon: Users,
        text: "Active",
        className: "bg-gradient-to-r from-blue-400 to-purple-500 text-white"
      });
    }
    
    return badges;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4">
          <Users className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Choose Your Community
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Select a Stokvel group in {wizardData.location} to join
        </p>
      </motion.div>

      {/* Groups List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-4 max-h-96 overflow-y-auto"
      >
        {wizardData.availableGroups.map((group, index) => {
          const badges = getGroupBadges(group);
          const isSelected = selectedGroupId === group._id;
          
          return (
            <motion.div
              key={group._id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card 
                className={`cursor-pointer transition-all duration-300 ${
                  isSelected
                    ? "ring-2 ring-emerald-500 bg-emerald-50 border-emerald-200 shadow-lg"
                    : "hover:shadow-md border-gray-200 hover:border-emerald-300"
                }`}
                onClick={() => handleGroupSelect(group)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Group Name and Location */}
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="text-lg font-bold text-gray-900 mb-1">
                            {group.name}
                          </h4>
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {group.geolocation}
                          </p>
                        </div>
                        {isSelected && (
                          <CheckCircle className="h-6 w-6 text-emerald-500 flex-shrink-0" />
                        )}
                      </div>

                      {/* Badges */}
                      {badges.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-3">
                          {badges.map((badge, badgeIndex) => (
                            <Badge 
                              key={badgeIndex}
                              className={`text-xs font-semibold ${badge.className}`}
                            >
                              <badge.icon className="h-3 w-3 mr-1" />
                              {badge.text}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Group Stats */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                          <div className="flex items-center gap-2 mb-1">
                            <Users className="h-4 w-4 text-blue-500" />
                            <span className="text-xs font-medium text-gray-600">Members</span>
                          </div>
                          <p className="text-lg font-bold text-gray-900">
                            {group.members?.length || 0}
                          </p>
                        </div>
                        
                        <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                          <div className="flex items-center gap-2 mb-1">
                            <Award className="h-4 w-4 text-green-500" />
                            <span className="text-xs font-medium text-gray-600">Total Saved</span>
                          </div>
                          <p className="text-lg font-bold text-gray-900">
                            {group.totalSales ? formatCurrency(group.totalSales) : formatCurrency(0)}
                          </p>
                        </div>
                      </div>

                      {/* Group Description */}
                      {group.description && (
                        <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                          {group.description}
                        </p>
                      )}

                      {/* Join Benefits */}
                      <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-3 border border-emerald-200">
                        <h5 className="text-sm font-semibold text-emerald-800 mb-2">
                          Benefits of joining this group:
                        </h5>
                        <ul className="text-xs text-emerald-700 space-y-1">
                          <li>• Bulk buying discounts up to 30%</li>
                          <li>• Weekly group orders and deliveries</li>
                          <li>• Community support and networking</li>
                          <li>• Shared transportation costs</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="flex gap-3 pt-4"
      >
        <Button
          variant="outline"
          onClick={() => goToStep("location-discovery")}
          className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Location
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!selectedGroupId}
          className="flex-1 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
        >
          <span className="flex items-center gap-2">
            Join This Community
            <ArrowRight className="h-4 w-4" />
          </span>
        </Button>
      </motion.div>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-center"
      >
        <p className="text-xs text-gray-500">
          You can always switch to a different group later if needed
        </p>
      </motion.div>
    </div>
  );
}
