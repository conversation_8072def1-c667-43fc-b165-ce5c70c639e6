// components/modals/community-steps/LocationDiscoveryStep.tsx

"use client";

import { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { MapPin, Search, ArrowRight, Users, TrendingUp, AlertTriangle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useGetAllStokvelGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice";
import type { CommunityWizardData, CommunityWizardStep } from "../JoinCommunityWizard";

interface LocationDiscoveryStepProps {
  wizardData: CommunityWizardData;
  updateWizardData: (data: Partial<CommunityWizardData>) => void;
  goToStep: (step: CommunityWizardStep) => void;
  onSuccess?: (user: any) => void;
  setIsLoading: (loading: boolean) => void;
}

export function LocationDiscoveryStep({ 
  wizardData, 
  updateWizardData, 
  goToStep,
  setIsLoading 
}: LocationDiscoveryStepProps) {
  const [location, setLocation] = useState(wizardData.location || "");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  const { data: groups = [] } = useGetAllStokvelGroupsQuery();

  // Stabilize the groups array to prevent infinite re-renders
  const stableGroups = useMemo(() => groups, [groups]);

  // Popular locations in Gauteng
  const popularLocations = useMemo(() => [
    "Soweto", "Johannesburg CBD", "Sandton", "Randburg", "Roodepoort",
    "Pretoria", "Centurion", "Midrand", "Kempton Park", "Benoni",
    "Boksburg", "Germiston", "Springs", "Alberton", "Edenvale"
  ], []);

  // Search for groups based on location
  useEffect(() => {
    if (!location || location.length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    // Simulate search delay for better UX
    const searchTimeout = setTimeout(() => {
      const filtered = stableGroups.filter(group =>
        group.geolocation?.toLowerCase().includes(location.toLowerCase())
      );

      setSearchResults(filtered);
      setIsSearching(false);
    }, 500);

    return () => clearTimeout(searchTimeout);
  }, [location, stableGroups]);

  const handleLocationChange = (value: string) => {
    setLocation(value);
    updateWizardData({ location: value });
  };

  const handleLocationSelect = (selectedLocation: string) => {
    setLocation(selectedLocation);
    updateWizardData({ location: selectedLocation });
  };

  const handleContinue = () => {
    if (!location || searchResults.length === 0) return;
    
    updateWizardData({ 
      location,
      availableGroups: searchResults 
    });
    goToStep("group-selection");
  };

  const isLocationValid = location.length >= 2;
  const hasResults = searchResults.length > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
          <MapPin className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Where are you located?
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Let's find Stokvel groups in your area
        </p>
      </motion.div>

      {/* Location Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-3"
      >
        <Label htmlFor="location" className="text-sm font-medium text-gray-700">
          Your Location
        </Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            id="location"
            placeholder="e.g. Soweto, Sandton, Pretoria..."
            value={location}
            onChange={(e) => handleLocationChange(e.target.value)}
            className="pl-10 h-12 text-base transition-all duration-200 border-gray-300 focus-visible:ring-emerald-500 focus-visible:border-emerald-500"
            style={{ fontFamily: "Avenir, sans-serif" }}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin" />
            </div>
          )}
        </div>
      </motion.div>

      {/* Popular Locations */}
      {!location && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-3"
        >
          <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Popular Locations
          </h4>
          <div className="flex flex-wrap gap-2">
            {popularLocations.map((loc) => (
              <button
                key={loc}
                onClick={() => handleLocationSelect(loc)}
                className="px-3 py-1 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-full text-sm transition-colors duration-200 border border-emerald-200 hover:border-emerald-300"
              >
                {loc}
              </button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Search Results */}
      {isLocationValid && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          {isSearching ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin mx-auto mb-3" />
              <p className="text-gray-600">Searching for groups in {location}...</p>
            </div>
          ) : hasResults ? (
            <>
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-5 w-5 text-emerald-600" />
                <h4 className="font-semibold text-gray-800">
                  Found {searchResults.length} group{searchResults.length !== 1 ? 's' : ''} in {location}
                </h4>
              </div>
              
              <div className="grid gap-3 max-h-64 overflow-y-auto">
                {searchResults.slice(0, 3).map((group) => (
                  <motion.div
                    key={group._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200 hover:border-emerald-300 transition-all duration-200"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h5 className="font-semibold text-gray-800 mb-1">
                          {group.name}
                        </h5>
                        <p className="text-sm text-gray-600 flex items-center gap-1 mb-2">
                          <MapPin className="h-3 w-3" />
                          {group.geolocation}
                        </p>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {group.members?.length || 0} members
                          </Badge>
                          {group.totalSales && (
                            <Badge variant="outline" className="text-xs">
                              R{group.totalSales.toLocaleString()} saved
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
                
                {searchResults.length > 3 && (
                  <div className="text-center py-2">
                    <p className="text-sm text-gray-600">
                      +{searchResults.length - 3} more group{searchResults.length - 3 !== 1 ? 's' : ''} available
                    </p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center p-6 bg-amber-50 border border-amber-200 rounded-xl"
            >
              <AlertTriangle className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h4 className="font-semibold text-amber-800 mb-2">
                No groups found in {location}
              </h4>
              <p className="text-sm text-amber-700 mb-4">
                Don't worry! We're constantly expanding. You can help start a new community in your area.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="border-amber-300 text-amber-700 hover:bg-amber-100"
              >
                Request New Group
              </Button>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Continue Button */}
      {hasResults && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="pt-4"
        >
          <Button
            onClick={handleContinue}
            className="w-full h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            <span className="flex items-center gap-2">
              View All Groups in {location}
              <ArrowRight className="h-4 w-4" />
            </span>
          </Button>
        </motion.div>
      )}
    </div>
  );
}
