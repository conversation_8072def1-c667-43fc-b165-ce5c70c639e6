// components/modals/wizard-steps/RelocationStep.tsx

"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { MapPin, Users, ArrowRight, ArrowLeft, CheckCircle, AlertTriangle, Navigation } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  useGetAllStokvelGroupsQuery,
  useJoinGroupMutation 
} from "@/lib/redux/features/groups/groupsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import type { WizardData, WizardStep } from "../JoinGroupWizard";

interface RelocationStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function RelocationStep({ 
  wizardData, 
  updateWizardData, 
  goToStep,
  onSuccess,
  productId,
  setIsLoading 
}: RelocationStepProps) {
  const [newLocation, setNewLocation] = useState("");
  const [selectedGroupId, setSelectedGroupId] = useState("");
  const [matchingGroups, setMatchingGroups] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API hooks
  const { data: groups = [] } = useGetAllStokvelGroupsQuery();
  const [joinGroup] = useJoinGroupMutation();
  const [addToCart] = useAddToCartMutation();

  // Filter groups based on new location
  useEffect(() => {
    if (!newLocation || newLocation.length < 2) {
      setMatchingGroups([]);
      return;
    }

    const filtered = groups.filter(group => 
      group.geolocation?.toLowerCase().includes(newLocation.toLowerCase()) &&
      group._id !== wizardData.membershipData?.group?._id // Exclude current group
    );
    setMatchingGroups(filtered);
  }, [newLocation, groups, wizardData.membershipData?.group?._id]);

  const handleLocationChange = (value: string) => {
    setNewLocation(value);
    
    // Clear selected group when location changes
    if (selectedGroupId) {
      setSelectedGroupId("");
    }
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroupId(groupId);
  };

  const handleRelocation = async () => {
    if (!selectedGroupId) return;

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      const userId = wizardData.userByEmailData?._id;
      if (!userId) {
        throw new Error("User ID not found");
      }

      // Relocate to new group
      const result = await joinGroup({
        userId,
        groupId: selectedGroupId,
        isRelocation: true
      }).unwrap();

      if (result.success) {
        // Add product to cart if provided
        if (productId) {
          try {
            await addToCart({
              userId,
              productId,
              quantity: 1,
              groupId: selectedGroupId
            }).unwrap();
          } catch (error) {
            console.error("Error adding product to cart:", error);
          }
        }

        // Success - close modal
        setTimeout(() => {
          onSuccess?.();
        }, 500);
      }
    } catch (error) {
      console.error("Error in relocation:", error);
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  const currentGroup = wizardData.membershipData?.group;
  const isLocationValid = newLocation.length >= 2;
  const canProceed = isLocationValid && selectedGroupId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-4">
          <Navigation className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Relocate to a new group
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Find a Stokvel group in your new location
        </p>
      </motion.div>

      {/* Current Group Info */}
      {currentGroup && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-blue-800">
                    Currently in: {currentGroup.groupName}
                  </h4>
                  <p className="text-sm text-blue-700">
                    {currentGroup.geolocation}
                  </p>
                </div>
                <Badge className="bg-blue-100 text-blue-800 border-blue-300">
                  Current
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* New Location Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-3"
      >
        <Label htmlFor="newLocation" className="text-sm font-medium text-gray-700">
          Your New Location
        </Label>
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <MapPin className="h-4 w-4 text-gray-400" />
          </div>
          <Input
            id="newLocation"
            placeholder="e.g. Johannesburg, Durban, Port Elizabeth"
            value={newLocation}
            onChange={(e) => handleLocationChange(e.target.value)}
            className={`pl-10 h-12 text-base transition-all duration-200 ${
              isLocationValid 
                ? "border-green-500 focus-visible:ring-green-500" 
                : "border-gray-300 focus-visible:ring-blue-500"
            }`}
            style={{ fontFamily: "Avenir, sans-serif" }}
          />
          {isLocationValid && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <CheckCircle className="h-4 w-4 text-green-500" />
            </div>
          )}
        </div>
      </motion.div>

      {/* Available Groups */}
      {isLocationValid && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          {matchingGroups.length > 0 ? (
            <>
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                <h4 className="font-semibold text-gray-800">
                  Available groups in {newLocation} ({matchingGroups.length})
                </h4>
              </div>
              
              <div className="grid gap-3 max-h-64 overflow-y-auto">
                {matchingGroups.map((group) => (
                  <motion.div
                    key={group._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card 
                      className={`cursor-pointer transition-all duration-200 ${
                        selectedGroupId === group._id
                          ? "ring-2 ring-orange-500 bg-orange-50 border-orange-200"
                          : "hover:shadow-md border-gray-200"
                      }`}
                      onClick={() => handleGroupSelect(group._id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h5 className="font-semibold text-gray-800 mb-1">
                              {group.name}
                            </h5>
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {group.geolocation}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {group.members?.length || 0} members
                              </Badge>
                              {group.totalSales && (
                                <Badge variant="outline" className="text-xs">
                                  R{group.totalSales.toLocaleString()} saved
                                </Badge>
                              )}
                            </div>
                          </div>
                          {selectedGroupId === group._id && (
                            <CheckCircle className="h-6 w-6 text-orange-500 flex-shrink-0" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center p-6 bg-amber-50 border border-amber-200 rounded-xl"
            >
              <AlertTriangle className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h4 className="font-semibold text-amber-800 mb-2">
                No groups found in {newLocation}
              </h4>
              <p className="text-sm text-amber-700">
                Unfortunately, there are no Stokvel groups available in this area yet.
                Try a nearby city or contact support to help start a new group.
              </p>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex gap-3 pt-4"
      >
        <Button
          variant="outline"
          onClick={() => goToStep("login")}
          className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button
          onClick={handleRelocation}
          disabled={!canProceed || isSubmitting}
          className="flex-1 h-12 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Relocating...
            </div>
          ) : (
            <span className="flex items-center gap-2">
              Confirm Relocation
              <ArrowRight className="h-4 w-4" />
            </span>
          )}
        </Button>
      </motion.div>

      {/* Warning Message */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl"
      >
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> Relocating will move you from your current group to the selected group. 
          This action cannot be undone easily.
        </p>
      </motion.div>
    </div>
  );
}
