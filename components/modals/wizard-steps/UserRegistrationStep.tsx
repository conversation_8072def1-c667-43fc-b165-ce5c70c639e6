// components/modals/wizard-steps/UserRegistrationStep.tsx

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { User, Phone, Lock, CheckCircle, AlertCircle, ArrowRight, ArrowLeft } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useAuthWithSignup } from "@/lib/redux/hooks/useAuthWithSignup";
import type { WizardData, WizardStep } from "../JoinGroupWizard";

interface UserRegistrationStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function UserRegistrationStep({ 
  wizardData, 
  updateWizardData, 
  goToStep,
  setIsLoading 
}: UserRegistrationStepProps) {
  const { signup } = useAuthWithSignup();
  
  const [formData, setFormData] = useState({
    name: wizardData.name || "",
    phone: wizardData.phone || "",
    password: wizardData.password || "",
    confirmPassword: ""
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation functions
  const validateName = (name: string) => name.length >= 3;
  const validatePhone = (phone: string) => /^\d{10,15}$/.test(phone);
  const validatePassword = (password: string) => password.length >= 6;
  const validateConfirmPassword = (password: string, confirmPassword: string) => 
    password === confirmPassword && validatePassword(password);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!validateName(formData.name)) {
      newErrors.name = "Name must be at least 3 characters";
    }

    if (!validatePhone(formData.phone)) {
      newErrors.phone = "Phone number must be 10-15 digits";
    }

    if (!validatePassword(formData.password)) {
      newErrors.password = "Password must be at least 6 characters";
    }

    if (!validateConfirmPassword(formData.password, formData.confirmPassword)) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      const newUser = await signup(
        formData.name,
        wizardData.email,
        formData.phone,
        formData.password
      );

      // Update wizard data with user info
      updateWizardData({
        name: formData.name,
        phone: formData.phone,
        password: formData.password,
        userByEmailData: newUser,
        isUserKnown: true
      });

      // Go to location selection
      goToStep("location-selection");
    } catch (error) {
      console.error("Registration error:", error);
      setErrors({ submit: "Registration failed. Please try again." });
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  const getFieldStatus = (field: string, validator: (value: string) => boolean) => {
    const value = formData[field as keyof typeof formData];
    if (!value) return "default";
    if (errors[field]) return "error";
    return validator(value) ? "success" : "error";
  };

  const getStatusIcon = (field: string, validator: (value: string) => boolean) => {
    const status = getFieldStatus(field, validator);
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const isFormValid = 
    validateName(formData.name) &&
    validatePhone(formData.phone) &&
    validatePassword(formData.password) &&
    validateConfirmPassword(formData.password, formData.confirmPassword);

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
          <User className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Create your account
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Join thousands of South Africans saving together
        </p>
      </motion.div>

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Name Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-2"
        >
          <Label htmlFor="name" className="text-sm font-medium text-gray-700">
            Full Name
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <User className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="name"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("name", validateName) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("name", validateName) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("name", validateName)}
            </div>
          </div>
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name}</p>
          )}
        </motion.div>

        {/* Phone Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
            Phone Number
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Phone className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="phone"
              placeholder="e.g. 0712345678"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("phone", validatePhone) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("phone", validatePhone) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("phone", validatePhone)}
            </div>
          </div>
          {errors.phone && (
            <p className="text-sm text-red-600">{errors.phone}</p>
          )}
        </motion.div>

        {/* Password Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-2"
        >
          <Label htmlFor="password" className="text-sm font-medium text-gray-700">
            Password
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Lock className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="password"
              type="password"
              placeholder="Create a secure password"
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("password", validatePassword) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("password", validatePassword) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("password", validatePassword)}
            </div>
          </div>
          {errors.password && (
            <p className="text-sm text-red-600">{errors.password}</p>
          )}
        </motion.div>

        {/* Confirm Password Field */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-2"
        >
          <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
            Confirm Password
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Lock className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
              className={`pl-10 pr-10 h-12 ${
                getFieldStatus("confirmPassword", (val) => validateConfirmPassword(formData.password, val)) === "success" 
                  ? "border-green-500 focus-visible:ring-green-500" 
                  : getFieldStatus("confirmPassword", (val) => validateConfirmPassword(formData.password, val)) === "error"
                  ? "border-red-500 focus-visible:ring-red-500"
                  : "border-gray-300"
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getStatusIcon("confirmPassword", (val) => validateConfirmPassword(formData.password, val))}
            </div>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-600">{errors.confirmPassword}</p>
          )}
        </motion.div>
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 bg-red-50 border border-red-200 rounded-xl"
        >
          <p className="text-sm text-red-700">{errors.submit}</p>
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="flex gap-3 pt-4"
      >
        <Button
          variant="outline"
          onClick={() => goToStep("email-verification")}
          className="flex-1 h-12 border-gray-300 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex-1 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Creating Account...
            </div>
          ) : (
            <span className="flex items-center gap-2">
              Create Account
              <ArrowRight className="h-4 w-4" />
            </span>
          )}
        </Button>
      </motion.div>
    </div>
  );
}
