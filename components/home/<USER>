"use client"

import { Users, MapPin, Truck, PiggyBank, Heart, Shield, Sparkles, TrendingUp } from 'lucide-react'
import { motion } from 'framer-motion'

const features = [
  {
    icon: Users,
    title: "Collective Power",
    description: "Unite with neighbors to unlock exclusive bulk discounts and premium pricing that individual shoppers can't access.",
    gradient: "from-blue-500 to-purple-600"
  },
  {
    icon: MapPin,
    title: "Smart Zones",
    description: "Hyper-local matching connects you with nearby shoppers for optimized delivery routes and community impact.",
    gradient: "from-green-500 to-teal-600"
  },
  {
    icon: Truck,
    title: "Efficient Delivery",
    description: "AI-powered logistics ensure efficient bulk distribution while minimizing environmental impact and costs.",
    gradient: "from-orange-500 to-red-600"
  },
  {
    icon: PiggyBank,
    title: "Guaranteed Value",
    description: "Transparent pricing with guaranteed savings of 20-40% compared to traditional retail through our model.",
    gradient: "from-pink-500 to-rose-600"
  }
]

export function ValueProposition() {
  return (
    <section className="relative py-32 px-4 md:px-6 bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Ccircle cx='50' cy='50' r='4'/%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3Ccircle cx='80' cy='20' r='2'/%3E%3Ccircle cx='20' cy='80' r='2'/%3E%3Ccircle cx='80' cy='80' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-block"
          >
            <h2
              className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent mb-6"
              style={{
                fontFamily: 'ClashDisplay-Variable, sans-serif',
                letterSpacing: '-0.03em'
              }}
            >
              The Stokvel
              <span className="block text-[#2A7C6C]">Advantage</span>
            </h2>
          </motion.div>

          <div className="w-32 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-8 rounded-full" />

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Experience the revolutionary benefits of community-powered shopping that&apos;s transforming
            how families across Gauteng access quality groceries.
          </motion.p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group relative bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 hover:border-[#2A7C6C]/30 shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2"
            >
              {/* Gradient Background on Hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`} />

              <div className="relative z-10">
                <div className={`h-16 w-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>

                <h3
                  className="text-[#2F4858] text-xl md:text-2xl font-bold mb-4 group-hover:text-[#2A7C6C] transition-colors duration-300"
                  style={{
                    fontFamily: 'ClashDisplay-Variable, sans-serif',
                    letterSpacing: '-0.01em'
                  }}
                >
                  {feature.title}
                </h3>

                <p
                  className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-12 rounded-3xl border border-[#2A7C6C]/20 max-w-4xl mx-auto">
            <h3
              className="text-3xl md:text-4xl font-bold text-[#2F4858] mb-6"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Ready to Experience the Difference?
            </h3>
            <p
              className="text-gray-600 text-lg md:text-xl mb-8 leading-relaxed max-w-2xl mx-auto"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Join thousands of families who have already discovered the power of community shopping.
              Start saving today while building meaningful connections in your neighborhood.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-10 py-4 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white rounded-full font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                <Heart className="h-5 w-5 mr-2 inline" />
                Join Your Community
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-10 py-4 border-2 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white rounded-full font-semibold transition-all duration-300"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                <Sparkles className="h-5 w-5 mr-2 inline" />
                Explore Features
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

