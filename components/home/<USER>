"use client"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { ArrowRight, Users, TrendingUp, Heart } from "lucide-react"

export function AboutSection() {
  const router = useRouter()

  const handleLearnMoreClick = () => {
    router.push("/aboutus")
  }

  return (
    <section className="relative py-32 px-4 md:px-6 bg-gradient-to-br from-[#2A7C6C] via-[#2A7C6C] to-[#1E5A4F] overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M50 50L25 75L75 75z'/%3E%3Cpath d='M50 50L75 25L75 75z'/%3E%3Cpath d='M50 50L25 25L75 25z'/%3E%3Cpath d='M50 50L25 25L25 75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Gradient Orbs */}
      <div className="absolute top-10 left-10 w-40 h-40 bg-gradient-to-r from-[#7FDBCA]/30 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-10 right-10 w-60 h-60 bg-gradient-to-l from-white/20 to-transparent rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-gradient-to-r from-[#7FDBCA]/20 to-transparent rounded-full blur-2xl" />

      <div className="container mx-auto relative z-10">
        <div className="grid md:grid-cols-2 gap-16 lg:gap-24 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="relative aspect-[4/4] w-full">
              {/* Decorative Elements */}
              <div className="absolute -top-4 -left-4 w-full h-full bg-gradient-to-br from-[#7FDBCA]/30 to-transparent rounded-3xl" />
              <div className="absolute -bottom-4 -right-4 w-full h-full bg-gradient-to-tl from-white/20 to-transparent rounded-3xl" />

              <Image
                src="/products/landingImage.png"
                alt="Community-driven shopping experience"
                fill
                className="object-cover rounded-3xl shadow-2xl relative z-10"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />

              {/* Floating Stats */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="absolute -top-6 -right-6 bg-white/90 backdrop-blur-sm p-4 rounded-2xl shadow-xl border border-white/20"
              >
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-[#2A7C6C]" />
                  <div>
                    <p className="text-sm font-semibold text-[#2F4858]">10,000+</p>
                    <p className="text-xs text-gray-600">Active Members</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-sm p-4 rounded-2xl shadow-xl border border-white/20"
              >
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-[#2A7C6C]" />
                  <div>
                    <p className="text-sm font-semibold text-[#2F4858]">35%</p>
                    <p className="text-xs text-gray-600">Avg. Savings</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-white text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.03em",
                }}
              >
                Building
                <span className="block text-[#7FDBCA]">Communities</span>
                <span className="block">Through Commerce</span>
              </motion.h1>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="w-24 h-1 bg-gradient-to-r from-[#7FDBCA] to-white rounded-full"
              />

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="text-white/90 text-xl md:text-2xl font-medium"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.01em",
                }}
              >
                Where tradition meets innovation in Gauteng&apos;s marketplace
              </motion.h2>
            </div>

            <div className="space-y-6">
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="text-white/90 text-lg md:text-xl leading-relaxed"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                We&apos;re reimagining how communities shop by blending the time-honored tradition of stokvels
                with cutting-edge technology. Our platform transforms everyday grocery shopping into a
                powerful collective experience that strengthens neighborhoods while delivering real savings.
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="text-white/80 text-lg leading-relaxed"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                Join the movement that&apos;s making premium groceries accessible to every family while
                fostering the spirit of ubuntu that makes our communities thrive.
              </motion.p>
            </div>

            {/* Feature Highlights */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="grid grid-cols-2 gap-4"
            >
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-2xl border border-white/20">
                <h4 className="text-white font-semibold mb-1" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Community Driven
                </h4>
                <p className="text-white/70 text-sm" style={{ fontFamily: "Avenir, sans-serif" }}>
                  Built by neighbors, for neighbors
                </p>
              </div>
              <div className="bg-white/10 backdrop-blur-sm p-4 rounded-2xl border border-white/20">
                <h4 className="text-white font-semibold mb-1" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                  Proven Savings
                </h4>
                <p className="text-white/70 text-sm" style={{ fontFamily: "Avenir, sans-serif" }}>
                  Up to 40% off retail prices
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="pt-4"
            >
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={handleLearnMoreClick}
                  className="group relative overflow-hidden bg-white text-[#2A7C6C] hover:bg-white/95 rounded-full px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <span className="relative z-10 flex items-center">
                    Discover Our Story
                    <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-[#7FDBCA] to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </Button>

                <Button
                  variant="outline"
                  onClick={() => router.push("/register")}
                  className="border-2 border-white/80 text-white bg-white/10 backdrop-blur-sm hover:bg-white hover:text-[#2A7C6C] hover:border-white rounded-full px-8 py-4 text-lg font-semibold transition-all duration-300 shadow-lg"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <Heart className="h-5 w-5 mr-2" />
                  Join Community
                </Button>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

