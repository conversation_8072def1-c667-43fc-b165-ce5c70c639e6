
// app/api/users/route.ts

import { NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/dbconnect"
import { User } from "@/models/User"
import { Referral } from "@/models/Referral"
import { generateAccessToken, generateRefreshToken } from "@/lib/auth"
import type { NextRequest } from "next/server"
import { getCorsHeaders } from "@/lib/cors"
import rateLimiter from "@/lib/rateLimit"
import { getClientIp } from "@/lib/getClientIp"
import Joi from "joi"
import type mongoose from "mongoose"
import crypto from "crypto"
import '@/models'; // Ensure all models are loaded

const registrationSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  phone: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .message("Phone number must be between 10 and 15 digits."),
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/)
    .message(
      "Password must be at least 8 characters long, contain uppercase and lowercase letters, a number, and a special character.",
    )
    .required(),
  referralCode: Joi.string().optional(),
})

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest
  const clientIp = getClientIp(nextReq)
  const origin = nextReq.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  if (nextReq.method === "OPTIONS") {
    return NextResponse.json({}, { headers: corsHeaders, status: 200 })
  }

  if (!rateLimiter.allowRequest(clientIp)) {
    console.warn(`Rate limit exceeded for IP: ${clientIp}`)
    return NextResponse.json(
      { error: "Too many requests. Please try again later." },
      { headers: corsHeaders, status: 429 },
    )
  }

  try {
    await connectToDatabase()

    const body = await req.json()
    const { error, value } = registrationSchema.validate(body)

    if (error) {
      return NextResponse.json({ error: error.details[0].message }, { headers: corsHeaders, status: 400 })
    }

    const { name, email, password, phone, referralCode } = value

    const existingUser = await User.findOne({ email })
    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists." },
        { headers: corsHeaders, status: 409 },
      )
    }

    let referrerId: mongoose.Types.ObjectId | undefined

    if (referralCode) {
      const referrer = await User.findOne({ referralCode })
      if (!referrer) {
        return NextResponse.json({ error: "Invalid referral code." }, { headers: corsHeaders, status: 400 })
      }
      referrerId = referrer._id

      const existingReferral = await Referral.findOne({
        referrerId: referrer._id,
        referralId: referralCode,
      })
      if (!existingReferral) {
        await new Referral({
          referrerId: referrer._id,
          referralId: referralCode,
        }).save()
      }
    }

    const newReferralCode = crypto.randomBytes(8).toString("hex")

    const newUser = new User({
      name,
      email,
      password,
      phone,
      role: "customer",
      referralCode: newReferralCode,
      referrerId: referrerId || null,
      tokenVersion: 0,
      rememberMe: false,
    })

    await newUser.save()

    if (referrerId) {
      await Referral.findOneAndUpdate({ referrerId, referralId: referralCode }, { referredUserId: newUser._id })
    }

    const accessToken = generateAccessToken(newUser._id.toString(), newUser.role)
    const refreshToken = await generateRefreshToken(newUser._id.toString(), newUser.role, newUser.tokenVersion, false)

    const responseData = {
      message: "Registration successful.",
      user: {
        id: newUser._id.toString(),
        email: newUser.email,
        name: newUser.name,
        phone: newUser.phone,
        role: newUser.role,
        referralCode: newUser.referralCode,
      },
      accessToken,
      refreshToken,
    }

    const clientType = nextReq.headers.get("x-client-type")?.toLowerCase() || "mobile"

    if (clientType === "web") {
      const response = NextResponse.json(
        { message: "User registered successfully.", user: responseData.user },
        { headers: corsHeaders, status: 201 },
      )

      response.cookies.set("accessToken", accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
        maxAge: 2 * 60 * 60,
      })

      response.cookies.set("refreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
        maxAge: 30 * 24 * 60 * 60,
      })

      return response
    } else {
      return NextResponse.json(responseData, { headers: corsHeaders, status: 201 })
    }
  } catch (error) {
    console.error("Registration error:", error)

    const errorMessage =
      process.env.NODE_ENV === "development" && error instanceof Error
        ? error.message
        : "An unexpected error occurred during registration. Please try again later."

    return NextResponse.json({ error: errorMessage }, { headers: corsHeaders, status: 500 })
  }
}

export async function OPTIONS(req: Request) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)
  return NextResponse.json({}, { headers: corsHeaders, status: 200 })
}

