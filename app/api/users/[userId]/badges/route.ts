// User Badges API
// Handles user badge management and retrieval

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user can access this data (self or admin)
    if (payload.userId !== params.userId && payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const rarity = searchParams.get('rarity');

    // Get user badges
    const allBadges = await gamificationService.getUserBadges(params.userId);
    
    // Filter badges based on query parameters
    let filteredBadges = allBadges;
    
    if (category) {
      filteredBadges = filteredBadges.filter(badge => badge.category === category);
    }
    
    if (rarity) {
      filteredBadges = filteredBadges.filter(badge => badge.rarity === rarity);
    }

    // Calculate summary statistics
    const summary = {
      totalBadges: allBadges.length,
      byCategory: {
        achievement: allBadges.filter(b => b.category === 'achievement').length,
        milestone: allBadges.filter(b => b.category === 'milestone').length,
        social: allBadges.filter(b => b.category === 'social').length,
        savings: allBadges.filter(b => b.category === 'savings').length,
        collaboration: allBadges.filter(b => b.category === 'collaboration').length,
        special: allBadges.filter(b => b.category === 'special').length
      },
      byRarity: {
        common: allBadges.filter(b => b.rarity === 'common').length,
        uncommon: allBadges.filter(b => b.rarity === 'uncommon').length,
        rare: allBadges.filter(b => b.rarity === 'rare').length,
        epic: allBadges.filter(b => b.rarity === 'epic').length,
        legendary: allBadges.filter(b => b.rarity === 'legendary').length
      },
      recentBadges: allBadges
        .sort((a, b) => new Date(b.earnedAt).getTime() - new Date(a.earnedAt).getTime())
        .slice(0, 5)
    };

    return NextResponse.json({
      success: true,
      data: filteredBadges,
      summary,
      metadata: {
        userId: params.userId,
        filters: { category, rarity },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('User badges GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user badges',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
