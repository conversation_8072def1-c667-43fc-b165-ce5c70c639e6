// User Achievements API
// Handles user achievement management and retrieval

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user can access this data (self or admin)
    if (payload.userId !== params.userId && payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const completed = searchParams.get('completed');

    // Get user achievements
    const allAchievements = await gamificationService.getUserAchievements(params.userId);
    
    // Filter achievements based on query parameters
    let filteredAchievements = allAchievements;
    
    if (type) {
      filteredAchievements = filteredAchievements.filter(achievement => achievement.type === type);
    }
    
    if (completed !== null) {
      const completedFilter = completed === 'true';
      filteredAchievements = filteredAchievements.filter(achievement => achievement.isCompleted === completedFilter);
    }

    // Calculate summary statistics
    const completedAchievements = allAchievements.filter(a => a.isCompleted);
    const totalPoints = completedAchievements.reduce((sum, a) => sum + a.points, 0);
    const completionRate = allAchievements.length > 0 
      ? (completedAchievements.length / allAchievements.length) * 100 
      : 0;

    const summary = {
      totalAchievements: allAchievements.length,
      completedAchievements: completedAchievements.length,
      totalPoints,
      completionRate,
      byType: {
        first_time: allAchievements.filter(a => a.type === 'first_time').length,
        milestone: allAchievements.filter(a => a.type === 'milestone').length,
        streak: allAchievements.filter(a => a.type === 'streak').length,
        social: allAchievements.filter(a => a.type === 'social').length,
        savings: allAchievements.filter(a => a.type === 'savings').length,
        collaboration: allAchievements.filter(a => a.type === 'collaboration').length
      },
      recentAchievements: completedAchievements
        .sort((a, b) => new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime())
        .slice(0, 5)
    };

    return NextResponse.json({
      success: true,
      data: filteredAchievements,
      summary,
      metadata: {
        userId: params.userId,
        filters: { type, completed },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('User achievements GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user achievements',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
