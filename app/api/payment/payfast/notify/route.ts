import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/modules/payments/payfast/server';

// Initialize PayFast service
const payFastService = new PayFastService({
  merchantId: process.env.PAYFAST_MERCHANT_ID!,
  merchantKey: process.env.PAYFAST_MERCHANT_KEY!,
  passphrase: process.env.PAYFAST_PASSPHRASE,
  sandbox: process.env.PAYFAST_SANDBOX === 'true',
  returnUrl: process.env.PAYFAST_RETURN_URL!,
  cancelUrl: process.env.PAYFAST_CANCEL_URL!,
  notifyUrl: process.env.PAYFAST_NOTIFY_URL!
});

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.text();
    const formData = new URLSearchParams(body);
    const webhookData = Object.fromEntries(formData.entries());

    console.log('PayFast webhook received:', webhookData);

    // Verify the webhook signature
    const isValid = payFastService.verifyNotification(webhookData);
    
    if (!isValid) {
      console.error('Invalid PayFast webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Extract payment information
    const {
      m_payment_id,
      pf_payment_id,
      payment_status,
      item_name,
      item_description,
      amount_gross,
      amount_fee,
      amount_net,
      custom_str1,
      custom_str2,
      custom_str3,
      custom_str4,
      custom_str5,
      name_first,
      name_last,
      email_address
    } = webhookData;

    // Process the payment status update
    const paymentStatus = {
      orderId: m_payment_id,
      transactionId: pf_payment_id,
      status: payment_status === 'COMPLETE' ? 'completed' : 'failed',
      amount: parseFloat(amount_gross || '0'),
      fee: parseFloat(amount_fee || '0'),
      netAmount: parseFloat(amount_net || '0'),
      customerName: `${name_first} ${name_last}`.trim(),
      customerEmail: email_address,
      description: item_description,
      customData: {
        str1: custom_str1,
        str2: custom_str2,
        str3: custom_str3,
        str4: custom_str4,
        str5: custom_str5
      }
    };

    // Here you would typically:
    // 1. Update the order status in your database
    // 2. Send confirmation emails
    // 3. Trigger any post-payment processes
    // 4. Update inventory
    // 5. Send notifications

    console.log('PayFast payment processed:', paymentStatus);

    // TODO: Implement your business logic here
    // await updateOrderStatus(paymentStatus);
    // await sendConfirmationEmail(paymentStatus);
    // await updateInventory(paymentStatus);

    // Respond with OK to acknowledge receipt
    return NextResponse.json({ status: 'OK' });

  } catch (error) {
    console.error('PayFast webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
