import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/modules/payments/payfast';

// Initialize PayFast service
const payFastService = new PayFastService({
  merchantId: process.env.PAYFAST_MERCHANT_ID!,
  merchantKey: process.env.PAYFAST_MERCHANT_KEY!,
  passphrase: process.env.PAYFAST_PASSPHRASE,
  sandbox: process.env.PAYFAST_SANDBOX === 'true',
  returnUrl: process.env.PAYFAST_RETURN_URL!,
  cancelUrl: process.env.PAYFAST_CANCEL_URL!,
  notifyUrl: process.env.PAYFAST_NOTIFY_URL!
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      description,
      customerEmail,
      customerName,
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerEmail || !customerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create PayFast payment
    const result = await payFastService.createPayment({
      orderId,
      amount,
      description,
      customerEmail,
      customerName,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('PayFast payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create PayFast payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
