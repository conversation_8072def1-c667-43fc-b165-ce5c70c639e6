import { NextRequest, NextResponse } from 'next/server';
import { PeachService } from '@/modules/payments/peach';

// Initialize Peach service
const peachService = new PeachService({
  entityId: process.env.PEACH_ENTITY_ID!,
  username: process.env.PEACH_USERNAME!,
  password: process.env.PEACH_PASSWORD!,
  sandbox: process.env.PEACH_SANDBOX === 'true',
  baseUrl: process.env.PEACH_BASE_URL!,
  successUrl: process.env.PEACH_SUCCESS_URL!,
  cancelUrl: process.env.PEACH_CANCEL_URL!,
  webhookUrl: process.env.PEACH_WEBHOOK_URL
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      currency = 'ZAR',
      description,
      customerEmail,
      customerName,
      paymentBrand = 'CARD',
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerEmail || !customerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create Peach payment
    const result = await peachService.createPayment({
      orderId,
      amount,
      currency,
      description,
      customerEmail,
      customerName,
      paymentBrand,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Peach payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create Peach payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
