import { NextRequest, NextResponse } from 'next/server';
import { PeachService } from '@/modules/payments/peach';

// Initialize Peach service
const peachService = new PeachService({
  entityId: process.env.PEACH_ENTITY_ID!,
  username: process.env.PEACH_USERNAME!,
  password: process.env.PEACH_PASSWORD!,
  sandbox: process.env.PEACH_SANDBOX === 'true',
  baseUrl: process.env.PEACH_BASE_URL!,
  successUrl: process.env.PEACH_SUCCESS_URL!,
  cancelUrl: process.env.PEACH_CANCEL_URL!,
  webhookUrl: process.env.PEACH_WEBHOOK_URL
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const checkoutId = searchParams.get('checkoutId');
    const orderId = searchParams.get('orderId');

    if (!checkoutId && !orderId) {
      return NextResponse.json(
        { error: 'Checkout ID or Order ID is required' },
        { status: 400 }
      );
    }

    let status;
    
    if (checkoutId) {
      // Get payment status by checkout ID
      status = await peachService.getPaymentStatus(checkoutId);
    } else {
      // For order ID, you would need to implement a lookup mechanism
      // This is a simplified implementation
      return NextResponse.json(
        { error: 'Order ID lookup not implemented' },
        { status: 501 }
      );
    }

    if (!status) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ status });

  } catch (error) {
    console.error('Peach status check error:', error);
    return NextResponse.json(
      { error: 'Failed to check payment status' },
      { status: 500 }
    );
  }
}
