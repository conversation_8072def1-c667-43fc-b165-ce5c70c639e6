import { NextRequest, NextResponse } from 'next/server';
import { CODService } from '@/modules/payments/cod';

// Initialize COD service
const codService = new CODService({
  enabled: true,
  maxAmount: 5000,
  minAmount: 50,
  deliveryFee: 50,
  deliveryFeeType: 'fixed',
  supportedAreas: ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'],
  estimatedDeliveryDays: 3,
  requiresPhoneVerification: true,
  requiresAddressVerification: true
});

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get COD payment status
    const status = await codService.getPaymentStatus(orderId);

    if (!status) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ status });

  } catch (error) {
    console.error('COD status check error:', error);
    return NextResponse.json(
      { error: 'Failed to check payment status' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const body = await request.json();
    const { status, notes } = body;

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Order ID and status are required' },
        { status: 400 }
      );
    }

    // Update COD payment status
    const result = await codService.updatePaymentStatus(orderId, status, notes);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to update status' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('COD status update error:', error);
    return NextResponse.json(
      { error: 'Failed to update payment status' },
      { status: 500 }
    );
  }
}
