import { NextRequest, NextResponse } from 'next/server';
import { CODService } from '@/modules/payments/cod';

// Initialize COD service
const codService = new CODService({
  enabled: true,
  maxAmount: 5000,
  minAmount: 50,
  deliveryFee: 50,
  deliveryFeeType: 'fixed',
  supportedAreas: ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'],
  estimatedDeliveryDays: 3,
  requiresPhoneVerification: true,
  requiresAddressVerification: true
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      currency = 'ZAR',
      description,
      customerName,
      customerEmail,
      customerPhone,
      deliveryAddress,
      specialInstructions,
      preferredDeliveryTime,
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerName || !customerEmail || !customerPhone || !deliveryAddress) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create COD payment
    const result = await codService.createPayment({
      orderId,
      amount,
      currency,
      description,
      customerName,
      customerEmail,
      customerPhone,
      deliveryAddress,
      specialInstructions,
      preferredDeliveryTime,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('COD payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create COD payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
