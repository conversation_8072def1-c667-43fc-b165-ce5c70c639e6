// Leaderboards API
// Handles group leaderboards and rankings

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const period = searchParams.get('period');

    // Get leaderboards
    const leaderboards: Record<string, any> = {};

    if (type && period) {
      // Get specific leaderboard
      const leaderboard = await gamificationService.getLeaderboard(type, period);
      if (leaderboard) {
        leaderboards[`${type}_${period}`] = leaderboard;
      }
    } else {
      // Get all leaderboards
      const leaderboardTypes = ['savings', 'activity', 'growth', 'collaboration', 'challenges'];
      const periods = ['weekly', 'monthly', 'all_time'];

      for (const lbType of leaderboardTypes) {
        for (const lbPeriod of periods) {
          const leaderboard = await gamificationService.getLeaderboard(lbType, lbPeriod);
          if (leaderboard) {
            leaderboards[`${lbType}_${lbPeriod}`] = leaderboard;
          }
        }
      }
    }

    // Calculate summary statistics
    const allEntries = Object.values(leaderboards).flatMap((lb: any) => lb.entries || []);
    const uniqueGroups = new Set(allEntries.map((entry: any) => entry.groupId));

    const summary = {
      totalLeaderboards: Object.keys(leaderboards).length,
      totalGroups: uniqueGroups.size,
      byType: {
        savings: Object.keys(leaderboards).filter(key => key.startsWith('savings_')).length,
        activity: Object.keys(leaderboards).filter(key => key.startsWith('activity_')).length,
        growth: Object.keys(leaderboards).filter(key => key.startsWith('growth_')).length,
        collaboration: Object.keys(leaderboards).filter(key => key.startsWith('collaboration_')).length,
        challenges: Object.keys(leaderboards).filter(key => key.startsWith('challenges_')).length
      },
      lastUpdated: Object.values(leaderboards).reduce((latest: Date, lb: any) => {
        const lbDate = new Date(lb.lastUpdated);
        return lbDate > latest ? lbDate : latest;
      }, new Date(0))
    };

    return NextResponse.json({
      success: true,
      data: leaderboards,
      summary,
      metadata: {
        filters: { type, period },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Leaderboards GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch leaderboards',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Only admins can trigger leaderboard updates
    if (payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update all leaderboards
    await gamificationService.updateGroupLeaderboards();

    return NextResponse.json({
      success: true,
      message: 'Leaderboards updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Leaderboards POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update leaderboards',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
