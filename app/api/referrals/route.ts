// app/api/referrals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { PromotionService } from '@/lib/services/promotionService';
import { getCorsHeaders } from '@/lib/cors';
import { 
  GenerateReferralCodeRequest, 
  ShareReferralRequest,
  ShareReferralResponse 
} from '@/types/promotions';

const promotionService = new PromotionService();

export async function POST(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const body = await request.json();

    switch (action) {
      case 'generate':
        return handleGenerateReferralCode(body, payload.userId, corsHeaders);
      case 'share':
        return handleShareReferral(body, payload.userId, corsHeaders);
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { headers: corsHeaders, status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in referrals API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleGenerateReferralCode(
  body: GenerateReferralCodeRequest,
  userId: string,
  corsHeaders: Record<string, string>
) {
  try {
    // Use default program ID if not provided
    const requestData = {
      userId: body.userId || userId,
      programId: body.programId || '000000000000000000000001'
    };

    const result = await promotionService.generateReferralCode(requestData);

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: result.success ? 200 : 400
    });
  } catch (error) {
    console.error('Error generating referral code:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate referral code' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleShareReferral(
  body: ShareReferralRequest,
  userId: string,
  corsHeaders: Record<string, string>
) {
  try {
    const { platform, referralCode } = body;
    
    // Generate the referral link
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://stokvel.co.za';
    const referralLink = `${baseUrl}/signup?ref=${referralCode}`;
    
    // Create platform-specific share URLs and messages
    let shareUrl = '';
    let message = '';
    
    const shareText = `🎉 Join me on Stokvel and get 10% off your first order! Use my referral code: ${referralCode}`;
    const encodedText = encodeURIComponent(shareText);
    const encodedUrl = encodeURIComponent(referralLink);

    switch (platform) {
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
        message = 'WhatsApp share link generated';
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
        message = 'Facebook share link generated';
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
        message = 'Twitter share link generated';
        break;
      case 'email':
        shareUrl = `mailto:?subject=Join me on Stokvel&body=${encodedText}%20${encodedUrl}`;
        message = 'Email share link generated';
        break;
      case 'copy_link':
        shareUrl = referralLink;
        message = 'Referral link ready to copy';
        break;
      default:
        shareUrl = referralLink;
        message = 'Generic share link generated';
    }

    // Award points for sharing (if not already awarded recently)
    try {
      await promotionService.earnLoyaltyPoints({
        userId,
        action: 'social_share',
        additionalData: { platform, referralCode }
      });
    } catch (pointsError) {
      console.warn('Could not award sharing points:', pointsError);
    }

    const response: ShareReferralResponse = {
      success: true,
      shareUrl,
      message
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error handling share referral:', error);
    const response: ShareReferralResponse = {
      success: false,
      error: 'Failed to generate share link'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}
