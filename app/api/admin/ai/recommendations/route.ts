// AI Recommendations API
// Handles AI-powered business recommendations and insights

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { aiRecommendationService } from '@/lib/services/aiRecommendationService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30d';
    const categories = searchParams.get('categories')?.split(',') || [];
    const priority = searchParams.get('priority');

    // Get AI recommendations
    const recommendations = await aiRecommendationService.generateRecommendations({
      timeframe,
      categories: categories.length > 0 ? categories : undefined,
      priority: priority || undefined
    });

    return NextResponse.json({
      success: true,
      data: recommendations,
      metadata: {
        totalRecommendations: recommendations.length,
        highPriorityCount: recommendations.filter(r => r.priority === 'critical' || r.priority === 'high').length,
        averageConfidence: recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length || 0,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('AI recommendations error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch AI recommendations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'generate':
        // Generate new recommendations
        const context = data?.context || {};
        const newRecommendations = await aiRecommendationService.generateRecommendations(context);
        
        return NextResponse.json({
          success: true,
          data: newRecommendations,
          message: 'New recommendations generated successfully'
        });

      case 'update_status':
        // Update recommendation status
        const { recommendationId, status } = data;
        const updated = await aiRecommendationService.updateRecommendationStatus(recommendationId, status);
        
        if (!updated) {
          return NextResponse.json(
            { error: 'Recommendation not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Recommendation status updated successfully'
        });

      case 'submit_feedback':
        // Submit feedback for recommendation
        const { recommendationId: feedbackId, feedback } = data;
        const feedbackSubmitted = await aiRecommendationService.submitRecommendationFeedback(feedbackId, {
          ...feedback,
          submittedAt: new Date(),
          submittedBy: payload.userId
        });
        
        if (!feedbackSubmitted) {
          return NextResponse.json(
            { error: 'Recommendation not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Feedback submitted successfully'
        });

      case 'bulk_update':
        // Bulk update multiple recommendations
        const { updates } = data;
        const results = await Promise.all(
          updates.map(async (update: any) => {
            return await aiRecommendationService.updateRecommendationStatus(update.id, update.status);
          })
        );

        return NextResponse.json({
          success: true,
          data: { updated: results.filter(Boolean).length, total: updates.length },
          message: 'Bulk update completed'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('AI recommendations POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process AI recommendations request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { id, status, feedback } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Recommendation ID is required' },
        { status: 400 }
      );
    }

    // Update recommendation status
    if (status) {
      const updated = await aiRecommendationService.updateRecommendationStatus(id, status);
      if (!updated) {
        return NextResponse.json(
          { error: 'Recommendation not found' },
          { status: 404 }
        );
      }
    }

    // Submit feedback if provided
    if (feedback) {
      const feedbackSubmitted = await aiRecommendationService.submitRecommendationFeedback(id, {
        ...feedback,
        submittedAt: new Date(),
        submittedBy: payload.userId
      });
      
      if (!feedbackSubmitted) {
        return NextResponse.json(
          { error: 'Failed to submit feedback' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Recommendation updated successfully'
    });

  } catch (error) {
    console.error('AI recommendations PUT error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update recommendation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
