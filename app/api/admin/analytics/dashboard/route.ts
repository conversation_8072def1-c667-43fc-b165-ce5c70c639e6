// Enhanced Admin Analytics Dashboard API
// Provides comprehensive analytics data for the admin dashboard

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { enhancedAnalyticsService } from '@/lib/services/enhancedAnalyticsService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get('from');
    const toDate = searchParams.get('to');
    const period = searchParams.get('period') || '30d'; // Default to 30 days

    // Calculate date range
    let dateRange: { from: Date; to: Date };
    
    if (fromDate && toDate) {
      dateRange = {
        from: new Date(fromDate),
        to: new Date(toDate)
      };
    } else {
      // Default date ranges based on period
      const now = new Date();
      const periodMap: Record<string, number> = {
        '7d': 7,
        '30d': 30,
        '90d': 90,
        '1y': 365
      };
      
      const days = periodMap[period] || 30;
      dateRange = {
        from: new Date(now.getTime() - (days * 24 * 60 * 60 * 1000)),
        to: now
      };
    }

    // Get comprehensive dashboard analytics
    const [
      dashboardMetrics,
      revenueAnalytics,
      customerAnalytics
    ] = await Promise.all([
      enhancedAnalyticsService.getAdvancedDashboardMetrics(dateRange),
      enhancedAnalyticsService.getRevenueAnalytics(dateRange),
      enhancedAnalyticsService.getCustomerAnalytics(dateRange)
    ]);

    // Structure the response
    const analyticsData = {
      overview: {
        period: period,
        dateRange: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString()
        },
        lastUpdated: new Date().toISOString()
      },
      metrics: dashboardMetrics,
      revenue: revenueAnalytics,
      customers: customerAnalytics,
      insights: await generateInsights(dashboardMetrics, revenueAnalytics, customerAnalytics),
      alerts: await getPerformanceAlerts(dashboardMetrics)
    };

    return NextResponse.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch dashboard analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Generate AI-powered insights from analytics data
async function generateInsights(
  metrics: any[],
  revenueAnalytics: any,
  customerAnalytics: any
): Promise<Insight[]> {
  const insights: Insight[] = [];

  // Revenue insights
  if (revenueAnalytics.revenueGrowth > 0.1) {
    insights.push({
      type: 'positive',
      category: 'revenue',
      title: 'Strong Revenue Growth',
      description: `Revenue has grown by ${(revenueAnalytics.revenueGrowth * 100).toFixed(1)}% in the selected period.`,
      impact: 'high',
      actionable: true,
      recommendations: [
        'Consider scaling marketing efforts to maintain growth momentum',
        'Analyze top-performing products for expansion opportunities'
      ]
    });
  }

  // Customer insights
  if (customerAnalytics.customerRetentionRate < 0.7) {
    insights.push({
      type: 'warning',
      category: 'customers',
      title: 'Customer Retention Opportunity',
      description: `Customer retention rate is ${(customerAnalytics.customerRetentionRate * 100).toFixed(1)}%, below the 70% benchmark.`,
      impact: 'medium',
      actionable: true,
      recommendations: [
        'Implement customer loyalty programs',
        'Analyze customer feedback for improvement opportunities',
        'Enhance customer support and engagement'
      ]
    });
  }

  // Performance insights
  const revenueMetric = metrics.find(m => m.name === 'Total Revenue');
  if (revenueMetric && revenueMetric.trend === 'up') {
    insights.push({
      type: 'positive',
      category: 'performance',
      title: 'Positive Revenue Trend',
      description: 'Revenue is trending upward with consistent growth patterns.',
      impact: 'high',
      actionable: false,
      recommendations: []
    });
  }

  return insights;
}

// Get performance alerts based on metrics
async function getPerformanceAlerts(metrics: any[]): Promise<Alert[]> {
  const alerts: Alert[] = [];

  // Check for critical metrics
  metrics.forEach(metric => {
    if (metric.category === 'performance' && metric.changePercentage < -20) {
      alerts.push({
        id: `alert_${metric.id}`,
        type: 'critical',
        title: `${metric.name} Performance Drop`,
        message: `${metric.name} has decreased by ${Math.abs(metric.changePercentage).toFixed(1)}%`,
        timestamp: new Date().toISOString(),
        resolved: false,
        actionRequired: true
      });
    }

    if (metric.category === 'revenue' && metric.changePercentage < -10) {
      alerts.push({
        id: `alert_${metric.id}`,
        type: 'warning',
        title: `${metric.name} Decline`,
        message: `${metric.name} has declined by ${Math.abs(metric.changePercentage).toFixed(1)}%`,
        timestamp: new Date().toISOString(),
        resolved: false,
        actionRequired: true
      });
    }
  });

  return alerts;
}

// Supporting interfaces
interface Insight {
  type: 'positive' | 'warning' | 'critical' | 'neutral';
  category: 'revenue' | 'customers' | 'orders' | 'products' | 'performance';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendations: string[];
}

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  actionRequired: boolean;
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'refresh_cache':
        // Clear analytics cache to force refresh
        // Implementation would clear the cache in enhancedAnalyticsService
        return NextResponse.json({
          success: true,
          message: 'Analytics cache refreshed successfully'
        });

      case 'export_data':
        // Export analytics data in specified format
        const { format, dateRange } = data;
        // Implementation would generate export file
        return NextResponse.json({
          success: true,
          downloadUrl: `/api/admin/analytics/export/${format}`,
          message: 'Export generated successfully'
        });

      case 'save_custom_metric':
        // Save custom metric configuration
        const { metricConfig } = data;
        // Implementation would save custom metric
        return NextResponse.json({
          success: true,
          message: 'Custom metric saved successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Dashboard analytics POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process analytics request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
