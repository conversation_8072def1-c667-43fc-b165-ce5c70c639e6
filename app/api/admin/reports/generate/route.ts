// Report Generation API
// Handles report generation and preview functionality

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { reportingService } from '@/lib/services/reportingService';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { configId, config, preview, customFilters } = body;

    // Generate report from existing config or provided config
    if (configId) {
      // Generate from existing configuration
      const generatedReport = await reportingService.generateReport(configId, customFilters);
      
      return NextResponse.json({
        success: true,
        data: generatedReport,
        message: 'Report generated successfully'
      });
    } else if (config) {
      // Generate from provided configuration (for preview or one-time generation)
      if (preview) {
        // Generate preview data
        const previewData = await generateReportPreview(config);
        
        return NextResponse.json({
          success: true,
          data: previewData,
          message: 'Report preview generated successfully'
        });
      } else {
        // Create temporary config and generate report
        const tempConfig = await reportingService.createReportConfig({
          ...config,
          createdBy: payload.userId,
          isActive: false // Mark as temporary
        });
        
        const generatedReport = await reportingService.generateReport(tempConfig.id);
        
        // Clean up temporary config
        await reportingService.deleteReportConfig(tempConfig.id);
        
        return NextResponse.json({
          success: true,
          data: generatedReport,
          message: 'Report generated successfully'
        });
      }
    } else {
      return NextResponse.json(
        { error: 'Either configId or config must be provided' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Report generation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate report',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get all generated reports
    const generatedReports = await reportingService.getGeneratedReports();

    return NextResponse.json({
      success: true,
      data: generatedReports
    });

  } catch (error) {
    console.error('Generated reports GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch generated reports',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Generate report preview data
async function generateReportPreview(config: any) {
  // Mock preview data - in production, this would generate actual preview
  const previewData = {
    summary: {
      title: config.name,
      description: config.description,
      period: 'Last 30 days',
      generatedAt: new Date(),
      keyMetrics: [
        {
          name: 'Total Revenue',
          value: 125000,
          unit: 'ZAR',
          change: 12.5,
          changeType: 'percentage',
          trend: 'up',
          format: 'currency'
        },
        {
          name: 'Total Orders',
          value: 450,
          unit: '',
          change: 8.2,
          changeType: 'percentage',
          trend: 'up',
          format: 'number'
        },
        {
          name: 'Average Order Value',
          value: 278,
          unit: 'ZAR',
          change: 3.1,
          changeType: 'percentage',
          trend: 'up',
          format: 'currency'
        }
      ],
      highlights: [
        'Revenue increased by 12.5% compared to previous period',
        'Order volume grew by 8.2% with improved conversion rates',
        'Customer acquisition cost decreased by 5%'
      ]
    },
    charts: config.visualizations.map((viz: any, index: number) => ({
      id: `chart_${index}`,
      type: viz.type,
      title: viz.title,
      data: generateMockChartData(viz.type),
      config: viz
    })),
    tables: [
      {
        id: 'top_products',
        title: 'Top Performing Products',
        headers: ['Product Name', 'Sales', 'Revenue', 'Growth'],
        rows: [
          ['Wireless Headphones', 125, 'R 31,250', '+15%'],
          ['Smart Watch', 98, 'R 29,400', '+12%'],
          ['Bluetooth Speaker', 87, 'R 17,400', '+8%'],
          ['Phone Case', 156, 'R 15,600', '+22%'],
          ['Charging Cable', 234, 'R 11,700', '+5%']
        ]
      }
    ],
    insights: [
      {
        type: 'positive',
        title: 'Strong Revenue Growth',
        description: 'Revenue has grown consistently over the reporting period with electronics leading the growth.',
        impact: 'high',
        recommendation: 'Consider expanding electronics inventory and marketing efforts.'
      },
      {
        type: 'neutral',
        title: 'Seasonal Trends Detected',
        description: 'Sales patterns show typical seasonal variations with peaks during weekends.',
        impact: 'medium',
        recommendation: 'Optimize inventory and staffing for weekend demand.'
      }
    ],
    metadata: {
      generatedAt: new Date(),
      executionTime: 1250,
      recordCount: 450,
      dataFreshness: new Date(),
      version: '1.0',
      filters: config.filters
    }
  };

  return previewData;
}

// Generate mock chart data based on chart type
function generateMockChartData(chartType: string) {
  switch (chartType) {
    case 'line':
    case 'area':
      return Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: Math.floor(Math.random() * 5000) + 2000,
        label: `Day ${i + 1}`
      }));
    
    case 'bar':
      return [
        { category: 'Electronics', value: 45000, percentage: 35 },
        { category: 'Clothing', value: 32000, percentage: 25 },
        { category: 'Home & Garden', value: 28000, percentage: 22 },
        { category: 'Books', value: 15000, percentage: 12 },
        { category: 'Sports', value: 8000, percentage: 6 }
      ];
    
    case 'pie':
      return [
        { name: 'Desktop', value: 45, color: '#8884d8' },
        { name: 'Mobile', value: 35, color: '#82ca9d' },
        { name: 'Tablet', value: 20, color: '#ffc658' }
      ];
    
    case 'metric':
      return {
        value: 125000,
        change: 12.5,
        trend: 'up',
        target: 150000,
        progress: 83.3
      };
    
    default:
      return [];
  }
}
