// Message Reactions API
// Handles emoji reactions on group messages

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string; messageId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { emoji } = body;

    if (!emoji) {
      return NextResponse.json(
        { error: 'Emoji is required' },
        { status: 400 }
      );
    }

    // Add reaction to message
    const success = await realTimeGroupService.addMessageReaction(
      params.messageId,
      payload.userId,
      emoji
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Reaction added successfully'
    });

  } catch (error) {
    console.error('Message reaction error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to add reaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
