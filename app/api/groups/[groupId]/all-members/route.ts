// app/api/groups/[groupId]/all-members/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCorsHeaders } from '@/lib/cors';
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from '@/models/User';
import { StokvelGroup } from '@/models/StokvelGroup';
import { verifyJWT } from '@/lib/auth';

export async function OPTIONS(req: NextRequest) {
    const origin = req.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
    request: NextRequest,
    { params }: { params: { groupId: string } }
) {
    const origin = request.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);

    try {
        // Verify authentication
        const token = request.headers.get('authorization')?.replace('Bearer ', '');
        if (!token) {
            return NextResponse.json(
                { error: 'No token provided' },
                { headers: corsHeaders, status: 401 }
            );
        }

        const payload = await verifyJWT(token);
        if (!payload) {
            return NextResponse.json(
                { error: 'Invalid token' },
                { headers: corsHeaders, status: 401 }
            );
        }

        const { groupId } = params;

        if (!groupId) {
            return NextResponse.json(
                { error: 'Group ID is required.' },
                { headers: corsHeaders, status: 400 }
            );
        }

        await connectToDatabase();

        // Find the group and populate member details
        const group = await StokvelGroup.findById(groupId)
            .populate({
                path: 'members',
                select: 'name email phone createdAt',
                model: 'User'
            })
            .select('members admin')
            .lean();

        if (!group) {
            return NextResponse.json(
                { error: 'Group not found.' },
                { headers: corsHeaders, status: 404 }
            );
        }

        // Check if the requesting user is a member of the group
        const isUserMember = group.members.some((member: any) =>
            member._id.toString() === payload.userId
        );

        if (!isUserMember) {
            return NextResponse.json(
                { error: 'You are not a member of this group.' },
                { headers: corsHeaders, status: 403 }
            );
        }

        // Format member data for the frontend
        const formattedMembers = group.members.map((member: any) => ({
            userId: member._id.toString(),
            name: member.name || 'N/A',
            email: member.email || 'N/A',
            phone: member.phone || 'N/A',
            totalOrders: '0', // TODO: Calculate from orders
            totalSpent: 'R0.00', // TODO: Calculate from orders
            joinedAt: member.createdAt ? member.createdAt.toISOString() : new Date().toISOString()
        }));

        return NextResponse.json(
            formattedMembers,
            { headers: corsHeaders, status: 200 }
        );

    } catch (error) {
        console.error('Failed to fetch group members:', error);
        return NextResponse.json(
            { error: 'Failed to fetch group members.' },
            { headers: corsHeaders, status: 500 }
        );
    }
}