// Online Members API
// Handles online member status and presence

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get group members with online status
    const members = await realTimeGroupService.getGroupMembers(params.groupId);

    return NextResponse.json({
      success: true,
      data: members,
      metadata: {
        groupId: params.groupId,
        totalMembers: members.length,
        onlineMembers: members.filter(m => m.isOnline).length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Online members GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch online members',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { status } = body; // 'online' or 'offline'

    if (!status || !['online', 'offline'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status (online or offline) is required' },
        { status: 400 }
      );
    }

    // Update member online status
    if (status === 'online') {
      await realTimeGroupService.setMemberOnline(params.groupId, payload.userId);
    } else {
      await realTimeGroupService.setMemberOffline(params.groupId, payload.userId);
    }

    return NextResponse.json({
      success: true,
      message: `Member status updated to ${status}`
    });

  } catch (error) {
    console.error('Online members POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update member status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
