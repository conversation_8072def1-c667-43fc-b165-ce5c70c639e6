// Collaborative Shopping Lists API
// Handles collaborative shopping list management

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get collaborative lists for the group
    const lists = await realTimeGroupService.getGroupLists(params.groupId);

    // Calculate summary statistics
    const summary = {
      totalLists: lists.length,
      activeLists: lists.filter(l => l.status === 'active').length,
      completedLists: lists.filter(l => l.status === 'completed').length,
      totalItems: lists.reduce((sum, list) => sum + list.items.length, 0),
      totalCollaborators: new Set(lists.flatMap(l => l.collaborators)).size
    };

    return NextResponse.json({
      success: true,
      data: lists,
      summary,
      metadata: {
        groupId: params.groupId,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Collaborative lists GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch collaborative lists',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, isPublic = true } = body;

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'List name is required' },
        { status: 400 }
      );
    }

    // Create collaborative list
    const list = await realTimeGroupService.createCollaborativeList(
      params.groupId,
      payload.userId,
      name,
      description || '',
      isPublic
    );

    return NextResponse.json({
      success: true,
      data: list,
      message: 'Collaborative list created successfully'
    });

  } catch (error) {
    console.error('Collaborative lists POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create collaborative list',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
