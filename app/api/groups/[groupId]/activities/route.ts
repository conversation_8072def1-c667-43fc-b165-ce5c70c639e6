// Group Activities API
// Handles group activity feed and activity management

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const type = searchParams.get('type');
    const timeRange = searchParams.get('timeRange');

    // Get group activities
    const activities = await realTimeGroupService.getGroupActivities(params.groupId, limit, offset);

    // Filter activities based on query parameters
    let filteredActivities = activities;

    if (type && type !== 'all') {
      const typeFilters: Record<string, string[]> = {
        members: ['member_joined', 'member_left'],
        products: ['product_added', 'product_removed', 'product_voted'],
        orders: ['order_placed', 'discount_achieved'],
        milestones: ['milestone_reached', 'challenge_completed'],
        chat: ['chat_message']
      };

      if (typeFilters[type]) {
        filteredActivities = filteredActivities.filter(activity => 
          typeFilters[type].includes(activity.type)
        );
      }
    }

    if (timeRange && timeRange !== 'all') {
      const now = new Date();
      let cutoffDate: Date;

      switch (timeRange) {
        case 'today':
          cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoffDate = new Date(0);
      }

      filteredActivities = filteredActivities.filter(activity => 
        new Date(activity.createdAt) >= cutoffDate
      );
    }

    // Calculate summary statistics
    const summary = {
      totalActivities: activities.length,
      filteredCount: filteredActivities.length,
      byType: {
        member_activities: activities.filter(a => ['member_joined', 'member_left'].includes(a.type)).length,
        product_activities: activities.filter(a => ['product_added', 'product_removed', 'product_voted'].includes(a.type)).length,
        order_activities: activities.filter(a => ['order_placed', 'discount_achieved'].includes(a.type)).length,
        milestone_activities: activities.filter(a => ['milestone_reached', 'challenge_completed'].includes(a.type)).length,
        chat_activities: activities.filter(a => a.type === 'chat_message').length
      },
      recentActivity: activities.length > 0 ? activities[0].createdAt : null
    };

    return NextResponse.json({
      success: true,
      data: filteredActivities,
      summary,
      metadata: {
        groupId: params.groupId,
        filters: { type, timeRange },
        limit,
        offset,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Group activities GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch group activities',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { type, title, description, metadata = {}, isPublic = true } = body;

    if (!type || !title || !description) {
      return NextResponse.json(
        { error: 'Type, title, and description are required' },
        { status: 400 }
      );
    }

    // Create activity
    const activity = await realTimeGroupService.createActivity(
      params.groupId,
      payload.userId,
      type,
      title,
      description,
      metadata,
      isPublic
    );

    return NextResponse.json({
      success: true,
      data: activity,
      message: 'Activity created successfully'
    });

  } catch (error) {
    console.error('Group activities POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create activity',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
