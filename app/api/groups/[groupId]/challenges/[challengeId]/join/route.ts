// Challenge Join API
// <PERSON><PERSON> joining group challenges

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string; challengeId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Join challenge
    const success = await gamificationService.joinChallenge(params.challengeId, payload.userId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to join challenge. Challenge may not exist, be inactive, or you may already be participating.' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully joined challenge'
    });

  } catch (error) {
    console.error('Challenge join error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to join challenge',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
