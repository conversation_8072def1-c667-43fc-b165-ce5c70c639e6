// Group Challenges API
// Handles group challenge management and participation

import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    // Get group challenges
    const allChallenges = await gamificationService.getGroupChallenges(params.groupId);
    
    // Filter challenges based on query parameters
    let filteredChallenges = allChallenges;
    
    if (status) {
      filteredChallenges = filteredChallenges.filter(challenge => challenge.status === status);
    }
    
    if (type) {
      filteredChallenges = filteredChallenges.filter(challenge => challenge.type === type);
    }

    // Calculate summary statistics
    const summary = {
      totalChallenges: allChallenges.length,
      activeChallenges: allChallenges.filter(c => c.status === 'active').length,
      upcomingChallenges: allChallenges.filter(c => c.status === 'upcoming').length,
      completedChallenges: allChallenges.filter(c => c.status === 'completed').length,
      totalParticipants: new Set(allChallenges.flatMap(c => c.participants.map(p => p.userId))).size,
      averageParticipation: allChallenges.length > 0 
        ? allChallenges.reduce((sum, c) => sum + c.participants.length, 0) / allChallenges.length 
        : 0
    };

    return NextResponse.json({
      success: true,
      data: filteredChallenges,
      summary,
      metadata: {
        groupId: params.groupId,
        filters: { status, type },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Group challenges GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch group challenges',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      title, 
      description, 
      type, 
      difficulty, 
      targetValue, 
      unit, 
      startDate, 
      endDate, 
      reward,
      rules = [],
      milestones = []
    } = body;

    // Validate required fields
    if (!title || !description || !type || !difficulty || !targetValue || !unit || !startDate || !endDate || !reward) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create challenge
    const challenge = await gamificationService.createChallenge(
      params.groupId,
      payload.userId,
      {
        title,
        description,
        type,
        difficulty,
        targetValue,
        unit,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        reward,
        rules,
        milestones
      }
    );

    return NextResponse.json({
      success: true,
      data: challenge,
      message: 'Challenge created successfully'
    });

  } catch (error) {
    console.error('Group challenges POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create challenge',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
