// app/api/loyalty/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { PromotionService } from '@/lib/services/promotionService';
import { getCorsHeaders } from '@/lib/cors';
import { 
  RedeemLoyaltyPointsRequest,
  EarnLoyaltyPointsRequest,
  WithdrawalRequest 
} from '@/types/promotions';

const promotionService = new PromotionService();

export async function POST(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const body = await request.json();

    switch (action) {
      case 'earn':
        return handleEarnPoints(body, payload.userId, corsHeaders);
      case 'redeem':
        return handleRedeemPoints(body, payload.userId, corsHeaders);
      case 'withdraw':
        return handleWithdrawal(body, payload.userId, corsHeaders);
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { headers: corsHeaders, status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in loyalty API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleEarnPoints(
  body: EarnLoyaltyPointsRequest,
  userId: string,
  corsHeaders: Record<string, string>
) {
  try {
    const requestData = {
      ...body,
      userId: body.userId || userId
    };

    const result = await promotionService.earnLoyaltyPoints(requestData);

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: result.success ? 200 : 400
    });
  } catch (error) {
    console.error('Error earning points:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to earn points' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleRedeemPoints(
  body: RedeemLoyaltyPointsRequest,
  userId: string,
  corsHeaders: Record<string, string>
) {
  try {
    const requestData = {
      ...body,
      userId: body.userId || userId
    };

    // Handle special redemption types
    if (body.rewardType === 'cash_withdrawal' || body.rewardType === 'delivery_payment') {
      return handleSpecialRedemption(requestData, corsHeaders);
    }

    const result = await promotionService.redeemLoyaltyPoints(requestData);

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: result.success ? 200 : 400
    });
  } catch (error) {
    console.error('Error redeeming points:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to redeem points' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleSpecialRedemption(
  data: RedeemLoyaltyPointsRequest,
  corsHeaders: Record<string, string>
) {
  try {
    // For cash withdrawal and delivery payments, we need special handling
    const { withdrawalDetails } = data;
    
    if (!withdrawalDetails) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Withdrawal details required for this redemption type' 
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Calculate cash value (10 points = R1)
    const cashValue = data.pointsCost / 10;
    
    if (cashValue < 10) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Minimum withdrawal amount is R10 (100 points)' 
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Create withdrawal request (in a real app, this would go to a payment processor)
    const withdrawalRequest = {
      userId: data.userId,
      amount: cashValue,
      pointsUsed: data.pointsCost,
      method: withdrawalDetails.method,
      status: 'pending',
      requestedAt: new Date(),
      ...(withdrawalDetails.method === 'bank_transfer' && {
        bankAccount: withdrawalDetails.bankAccount
      })
    };

    // For now, we'll simulate the withdrawal process
    // In production, this would integrate with payment providers
    
    return NextResponse.json({
      success: true,
      message: `Withdrawal request submitted for R${cashValue}`,
      withdrawalId: `WD${Date.now()}`,
      amount: cashValue,
      pointsUsed: data.pointsCost,
      estimatedProcessingTime: '2-3 business days'
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error processing special redemption:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process withdrawal request' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

async function handleWithdrawal(
  body: WithdrawalRequest,
  userId: string,
  corsHeaders: Record<string, string>
) {
  try {
    const requestData = {
      ...body,
      userId: body.userId || userId
    };

    // Validate minimum withdrawal amount
    if (requestData.amount < 10) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Minimum withdrawal amount is R10' 
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate points requirement (10 points = R1)
    const requiredPoints = requestData.amount * 10;
    if (requestData.pointsUsed < requiredPoints) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Insufficient points. Required: ${requiredPoints} points` 
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Create withdrawal request
    const withdrawalId = `WD${Date.now()}`;
    
    // In production, this would:
    // 1. Deduct points from user's account
    // 2. Create withdrawal record in database
    // 3. Queue for payment processing
    // 4. Send confirmation email/SMS

    return NextResponse.json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      withdrawalId,
      amount: requestData.amount,
      pointsUsed: requestData.pointsUsed,
      method: requestData.method,
      estimatedProcessingTime: '2-3 business days',
      status: 'pending'
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error processing withdrawal:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process withdrawal request' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
