// app/api/loyalty/user/[userId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import mongoose from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyJWT(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    // Check if user can access this data (self or admin)
    if (payload.userId !== params.userId && payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { headers: corsHeaders, status: 403 }
      );
    }

    await connectToDatabase();

    // Get or create UserLoyalty model
    const UserLoyaltyModel = mongoose.model('UserLoyalty', new mongoose.Schema({
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
      programId: { type: mongoose.Schema.Types.ObjectId, required: true },
      totalPoints: { type: Number, default: 0 },
      availablePoints: { type: Number, default: 0 },
      currentTier: { type: String, default: 'Bronze' },
      tierProgress: {
        currentPoints: { type: Number, default: 0 },
        nextTierPoints: { type: Number, default: 1000 },
        progressPercentage: { type: Number, default: 0 }
      },
      pointsHistory: [{
        action: String,
        points: Number,
        description: String,
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'GroupOrder' },
        expiresAt: Date,
        createdAt: { type: Date, default: Date.now }
      }],
      redemptionHistory: [{
        rewardType: String,
        pointsUsed: Number,
        rewardValue: Number,
        couponCode: String,
        redeemedAt: { type: Date, default: Date.now }
      }],
      lastActivity: { type: Date, default: Date.now }
    }, { timestamps: true }));

    // Find or create user loyalty record
    let userLoyalty = await UserLoyaltyModel.findOne({ userId: params.userId });
    
    if (!userLoyalty) {
      userLoyalty = new UserLoyaltyModel({
        userId: params.userId,
        programId: new mongoose.Types.ObjectId('000000000000000000000001'), // Default program
        totalPoints: 0,
        availablePoints: 0,
        currentTier: 'Bronze',
        tierProgress: {
          currentPoints: 0,
          nextTierPoints: 1000,
          progressPercentage: 0
        },
        pointsHistory: [],
        redemptionHistory: [],
        lastActivity: new Date()
      });
      await userLoyalty.save();
    }

    // Calculate tier information
    const tiers = [
      { name: 'Bronze', requiredPoints: 0, benefits: ['Basic rewards', 'Welcome bonus'] },
      { name: 'Silver', requiredPoints: 1000, benefits: ['5% bonus points', 'Free shipping on orders over R200'] },
      { name: 'Gold', requiredPoints: 5000, benefits: ['10% bonus points', 'Priority support', 'Exclusive deals'] },
      { name: 'Platinum', requiredPoints: 15000, benefits: ['15% bonus points', 'Exclusive products', 'Personal shopper'] }
    ];

    const currentTierIndex = tiers.findIndex(tier => tier.name === userLoyalty.currentTier);
    const currentTier = tiers[currentTierIndex];
    const nextTier = tiers[currentTierIndex + 1];

    // Update tier progress
    if (nextTier) {
      const progressToNext = userLoyalty.totalPoints - currentTier.requiredPoints;
      const pointsNeededForNext = nextTier.requiredPoints - currentTier.requiredPoints;
      const progressPercentage = Math.min(100, (progressToNext / pointsNeededForNext) * 100);
      
      userLoyalty.tierProgress = {
        currentPoints: progressToNext,
        nextTierPoints: pointsNeededForNext,
        progressPercentage
      };
    }

    // Calculate redemption options
    const redemptionOptions = [
      {
        type: 'discount',
        name: '5% Discount Coupon',
        pointsCost: 500,
        value: 5,
        description: '5% off your next order',
        available: userLoyalty.availablePoints >= 500
      },
      {
        type: 'discount',
        name: '10% Discount Coupon',
        pointsCost: 1000,
        value: 10,
        description: '10% off your next order',
        available: userLoyalty.availablePoints >= 1000
      },
      {
        type: 'free_shipping',
        name: 'Free Shipping',
        pointsCost: 200,
        value: 0,
        description: 'Free shipping on your next order',
        available: userLoyalty.availablePoints >= 200
      },
      {
        type: 'delivery_payment',
        name: 'Delivery Payment',
        pointsCost: 300,
        value: 30,
        description: 'R30 towards delivery costs',
        available: userLoyalty.availablePoints >= 300
      },
      {
        type: 'cash_withdrawal',
        name: 'Cash Withdrawal',
        pointsCost: 1000,
        value: 100,
        description: 'Withdraw R100 cash (minimum)',
        available: userLoyalty.availablePoints >= 1000
      }
    ];

    // Calculate statistics
    const totalEarned = userLoyalty.pointsHistory.reduce((sum, entry) => sum + entry.points, 0);
    const totalRedeemed = userLoyalty.redemptionHistory.reduce((sum, entry) => sum + entry.pointsUsed, 0);
    const totalCashValue = Math.floor(userLoyalty.availablePoints / 10); // 10 points = R1

    const response = {
      success: true,
      data: {
        userId: params.userId,
        totalPoints: userLoyalty.totalPoints,
        availablePoints: userLoyalty.availablePoints,
        currentTier: userLoyalty.currentTier,
        tierProgress: userLoyalty.tierProgress,
        nextTier: nextTier?.name || null,
        tierBenefits: currentTier.benefits,
        pointsHistory: userLoyalty.pointsHistory.slice(-10), // Last 10 entries
        redemptionHistory: userLoyalty.redemptionHistory.slice(-10), // Last 10 entries
        redemptionOptions,
        statistics: {
          totalEarned,
          totalRedeemed,
          totalCashValue,
          lifetimeValue: Math.floor(totalEarned / 10) // Total cash equivalent earned
        },
        lastActivity: userLoyalty.lastActivity
      }
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching user loyalty data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch loyalty data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
