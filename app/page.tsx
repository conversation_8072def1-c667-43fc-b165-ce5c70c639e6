// app/page.tsx
"use client"

import { useState } from "react"
import { AboutSection } from "@/components/about/AboutSection"
import { FullWidthVideo } from "@/components/home/<USER>"
import { HeroSlider } from "@/components/home/<USER>"
import { MembershipSection } from "@/components/home/<USER>"
// import { PricingSection } from "@/components/home/<USER>"
import { ProductListing } from "@/components/home/<USER>"
import { ValueProposition } from "@/components/about/ValueProposition"
import { JoinCommunityModal } from "@/components/modals/JoinCommunityModal"

export default function Home() {
  const [isJoinCommunityOpen, setIsJoinCommunityOpen] = useState(false)

  const openJoinCommunity = () => setIsJoinCommunityOpen(true)
  const closeJoinCommunity = () => setIsJoinCommunityOpen(false)

  return (
    <main>
      <HeroSlider/>
      <MembershipSection />
      <AboutSection onJoinCommunity={openJoinCommunity} />
      <ValueProposition onJoinCommunity={openJoinCommunity} />
      <ProductListing/>
      <FullWidthVideo onJoinCommunity={openJoinCommunity} />
      {/* <PricingSection/> */}

      <JoinCommunityModal
        isOpen={isJoinCommunityOpen}
        onClose={closeJoinCommunity}
      />
    </main>
  )
}
