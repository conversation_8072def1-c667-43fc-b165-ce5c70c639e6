"use client";

import { ReferralDashboard } from "@/components/referrals/ReferralDashboard";
import { ReferralDashboardSimple } from "@/components/referrals/ReferralDashboardSimple";
import { PremiumReferralDashboard } from "@/components/referrals/PremiumReferralDashboard";
import { useAuth } from "@/context/AuthContext";
import { LoadingScreen } from "@/components/ui/loading-screen";

export default function ReferralsPage() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Please log in
          </h2>
          <p className="text-gray-600">
            You need to be logged in to view your referral dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-8">
      {/* Premium Commercial-Level Dashboard */}
      <PremiumReferralDashboard userId={user._id} />

      {/* Alternative versions for testing */}
      {/* <ReferralDashboardSimple userId={user._id} /> */}
      {/* <ReferralDashboard userId={user._id} /> */}
    </div>
  );
}
