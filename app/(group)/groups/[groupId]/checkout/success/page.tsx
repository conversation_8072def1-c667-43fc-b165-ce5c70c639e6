'use client'

import { useParams, useSearchParams } from 'next/navigation'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { OrderSuccessMessage } from '@/components/cart/OrderSuccessMessage'
import { Skeleton } from '@/components/ui/skeleton'

export default function CheckoutSuccessPage() {
  const params = useParams<{ groupId: string }>()
  const searchParams = useSearchParams()
  const router = useRouter()
  
  // Get the order ID from the URL query parameters
  const orderId = searchParams.get('orderId')
  
  // Redirect to orders page if no order ID is provided
  useEffect(() => {
    if (!orderId) {
      router.push(`/groups/${params.groupId}/orders`)
    }
  }, [orderId, router, params.groupId])
  
  // Show loading state while checking order ID
  if (!orderId) {
    return (
      <div className="container py-12">
        <div className="max-w-2xl mx-auto">
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
      </div>
    )
  }
  
  return (
    <div className="container py-12">
      <OrderSuccessMessage groupId={params.groupId} orderId={orderId} />
    </div>
  )
}
