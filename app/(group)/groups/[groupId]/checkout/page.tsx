'use client'

import { useParams } from 'next/navigation'
import { GroupOrderReduxCheckout } from '@/components/group-orders/GroupOrderReduxCheckout'
import { Card, CardContent } from '@/components/ui/card'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectTotalItems, selectSubtotal } from '@/lib/redux/features/cart/cartSlice'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Skeleton } from '@/components/ui/skeleton'
import { DiscountProgressBar } from '@/components/cart/DiscountProgressBar'
import { CartLoadingIndicator } from '@/components/cart/CartLoadingIndicator'
import { ShoppingBag, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'

export default function GroupCheckoutPage() {
  const params = useParams<{ groupId: string }>()
  const router = useRouter()
  const totalItems = useAppSelector(selectTotalItems)
  const subtotal = useAppSelector(selectSubtotal)

  // Redirect to products page if cart is empty
  useEffect(() => {
    if (totalItems === 0) {
      router.push(`/groups/${params.groupId}/products`)
    }
  }, [totalItems, router, params.groupId])

  // Show loading state while checking cart items
  if (totalItems === 0) {
    return (
      <div className="container py-8">
        <Skeleton className="h-12 w-1/3 mb-6" />
        <div className="grid gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      {/* Loading indicator */}
      <CartLoadingIndicator />

      {/* Back button */}
      <Button
        variant="ghost"
        className="mb-4 flex items-center gap-2 text-gray-600 hover:text-gray-900"
        onClick={() => router.back()}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Cart
      </Button>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-purple-100 p-2 rounded-full">
            <ShoppingBag className="h-6 w-6 text-purple-600" />
          </div>
          <h1 className="text-3xl font-bold">Checkout</h1>
        </div>

        {/* Order summary */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                <GroupOrderReduxCheckout groupId={params.groupId} />

                {/* Debug cart information - removed for production */}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {/* Discount progress */}
            <DiscountProgressBar groupId={params.groupId} />

            {/* Order summary card */}
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-medium text-lg mb-4">Order Summary</h3>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Items ({totalItems})</span>
                    <span>R {subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span className="text-green-600">Free</span>
                  </div>
                </div>
                <div className="border-t pt-4 flex justify-between font-medium">
                  <span>Total</span>
                  <span>R {subtotal.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
