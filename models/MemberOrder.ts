// models/MemberOrder.ts

import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IMemberOrderItem {
  product: mongoose.Types.ObjectId;
  quantity: number;
  unitPrice: number;
  subtotal: number;
}

export enum MemberOrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  PACKED = 'packed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

export interface IMemberOrder extends Document {
  userId: mongoose.Types.ObjectId;
  groupId: mongoose.Types.ObjectId;
  groupOrderId: mongoose.Types.ObjectId;
  orderNumber: string; // Unique order number for member
  items: IMemberOrderItem[];
  totalAmount: number;
  status: MemberOrderStatus;
  statusHistory: {
    status: MemberOrderStatus;
    timestamp: Date;
    notes?: string;
  }[];
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    country: string;
    postalCode: string;
    phone?: string;
  };
  paymentInfo: {
    method: string;
    status: 'pending' | 'paid' | 'failed' | 'refunded';
    transactionId?: string;
    paidAt?: Date;
  };
  shippingInfo?: {
    trackingNumber?: string;
    carrier?: string;
    estimatedDelivery?: Date;
    actualDelivery?: Date;
    shippingAddress?: string;
  };
  discountApplied?: {
    type: 'bulk' | 'coupon' | 'loyalty';
    amount: number;
    percentage: number;
  };
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Member Order Item Schema
const MemberOrderItemSchema: Schema = new Schema({
  product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
  quantity: { type: Number, required: true, min: 1 },
  unitPrice: { type: Number, required: true, min: 0 },
  subtotal: { type: Number, required: true, min: 0 }
});

// Customer Info Schema
const CustomerInfoSchema: Schema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  address: { type: String, required: true },
  city: { type: String, required: true },
  country: { type: String, required: true },
  postalCode: { type: String, required: true },
  phone: { type: String }
});

// Payment Info Schema
const PaymentInfoSchema: Schema = new Schema({
  method: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['pending', 'paid', 'failed', 'refunded'], 
    default: 'pending' 
  },
  transactionId: { type: String },
  paidAt: { type: Date }
});

// Shipping Info Schema
const ShippingInfoSchema: Schema = new Schema({
  trackingNumber: { type: String },
  carrier: { type: String },
  estimatedDelivery: { type: Date },
  actualDelivery: { type: Date },
  shippingAddress: { type: String }
});

// Discount Applied Schema
const DiscountAppliedSchema: Schema = new Schema({
  type: { type: String, enum: ['bulk', 'coupon', 'loyalty'], required: true },
  amount: { type: Number, required: true, min: 0 },
  percentage: { type: Number, required: true, min: 0, max: 100 }
});

// Status History Schema
const StatusHistorySchema: Schema = new Schema({
  status: { 
    type: String, 
    enum: Object.values(MemberOrderStatus), 
    required: true 
  },
  timestamp: { type: Date, default: Date.now },
  notes: { type: String }
});

// Member Order Schema
const MemberOrderSchema: Schema<IMemberOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    groupId: { type: Schema.Types.ObjectId, ref: 'StokvelGroup', required: true, index: true },
    groupOrderId: { type: Schema.Types.ObjectId, ref: 'GroupOrder', required: true, index: true },
    orderNumber: { 
      type: String, 
      required: true, 
      unique: true,
      index: true
    },
    items: [MemberOrderItemSchema],
    totalAmount: { type: Number, required: true, min: 0 },
    status: {
      type: String,
      enum: Object.values(MemberOrderStatus),
      default: MemberOrderStatus.PENDING,
      index: true
    },
    statusHistory: [StatusHistorySchema],
    customerInfo: { type: CustomerInfoSchema, required: true },
    paymentInfo: { type: PaymentInfoSchema, required: true },
    shippingInfo: ShippingInfoSchema,
    discountApplied: DiscountAppliedSchema,
    notes: { type: String, maxlength: 1000 }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for efficient queries
MemberOrderSchema.index({ userId: 1, createdAt: -1 });
MemberOrderSchema.index({ groupId: 1, status: 1 });
MemberOrderSchema.index({ groupOrderId: 1 });
MemberOrderSchema.index({ orderNumber: 1 });
MemberOrderSchema.index({ 'paymentInfo.status': 1 });
MemberOrderSchema.index({ status: 1, createdAt: -1 });

// Virtual for order age
MemberOrderSchema.virtual('orderAge').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to generate order number
MemberOrderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const count = await mongoose.model('MemberOrder').countDocuments();
    this.orderNumber = `MO${Date.now()}${(count + 1).toString().padStart(4, '0')}`;
  }
  next();
});

// Pre-save middleware to update status history
MemberOrderSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date(),
      notes: `Status changed to ${this.status}`
    });
  }
  next();
});

// Static methods
MemberOrderSchema.statics.findByUser = function(userId: string) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

MemberOrderSchema.statics.findByGroup = function(groupId: string) {
  return this.find({ groupId }).sort({ createdAt: -1 });
};

MemberOrderSchema.statics.findByGroupOrder = function(groupOrderId: string) {
  return this.find({ groupOrderId }).sort({ createdAt: -1 });
};

// Instance methods
MemberOrderSchema.methods.updateStatus = function(newStatus: MemberOrderStatus, notes?: string) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    notes: notes || `Status updated to ${newStatus}`
  });
  return this.save();
};

MemberOrderSchema.methods.markAsPaid = function(transactionId?: string) {
  this.paymentInfo.status = 'paid';
  this.paymentInfo.paidAt = new Date();
  if (transactionId) {
    this.paymentInfo.transactionId = transactionId;
  }
  return this.save();
};

// Export the model
export const MemberOrder = mongoose.models.MemberOrder || mongoose.model<IMemberOrder>('MemberOrder', MemberOrderSchema);

export type MemberOrderModel = typeof MemberOrder;
