

// models/StokvelGroup.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface IStokvelGroup extends Document {
  name: string;
  description: string;
  members: mongoose.Types.ObjectId[];
  admin: mongoose.Types.ObjectId;
  geolocation: string;
  totalSales: number;
  avgOrderValue: number;
  activeOrders: number;
  bulkOrderThreshold: number; // Minimum order amount for bulk purchasing
  pendingOrderAmount: number; // Current pending order total
  deliveryStatus: 'pending' | 'in-transit' | 'delivered';
  createdAt: Date;
  updatedAt: Date;
}

const StokvelGroupSchema: Schema<IStokvelGroup> = new Schema(
  {
    name: { type: String, required: true },
    description: { type: String, required: true },
    members: [{ type: mongoose.Types.ObjectId, ref: 'User' }],
    admin: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    geolocation: { type: String, required: true },
    totalSales: { type: Number, default: 0 },
    avgOrderValue: { type: Number, default: 0 },
    activeOrders: { type: Number, default: 0 },
    bulkOrderThreshold: { type: Number, default: 1000 },
    pendingOrderAmount: { type: Number, default: 0 },
    deliveryStatus: { type: String, default: 'pending' },
  },
  { timestamps: true }
);

// Indexes for efficient queries
StokvelGroupSchema.index({ name: 1 });
StokvelGroupSchema.index({ admin: 1 });
StokvelGroupSchema.index({ members: 1 });

// Export the model
export const StokvelGroup = mongoose.models.StokvelGroup || model<IStokvelGroup>('StokvelGroup', StokvelGroupSchema);
