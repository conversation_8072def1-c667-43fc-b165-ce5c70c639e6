// models/Coupon.ts
import mongoose, { Schema, Document, Model } from 'mongoose';
import { 
  CouponType, 
  DiscountTarget, 
  CouponStatus,
  ICoupon 
} from '@/types/promotions';

// Coupon type enum values
const CouponTypeValues = [
  'percentage',
  'fixed_amount',
  'free_shipping',
  'buy_x_get_y',
  'bulk_discount'
] as const;

// Discount target enum values
const DiscountTargetValues = [
  'order_total',
  'specific_products',
  'category',
  'shipping',
  'group_orders'
] as const;

// Status enum values
const CouponStatusValues = [
  'active',
  'inactive',
  'expired',
  'used_up',
  'scheduled'
] as const;

// Interface for model methods and statics
interface ICouponModel extends Model<ICoupon> {
  generateUniqueCode(prefix?: string, length?: number): Promise<string>;
  findValidCoupons(filters?: any): Promise<ICoupon[]>;
  validateCoupon(code: string, userId: string, orderData: any): Promise<{
    valid: boolean;
    coupon?: ICoupon;
    discountAmount?: number;
    error?: string;
  }>;
  calculateDiscount(coupon: ICoupon, orderData: any): number;
  incrementUsage(couponId: string, userId: string): Promise<ICoupon | null>;
  getUsageStats(couponId: string): Promise<any>;
  cleanupExpired(): Promise<number>;
}

// Coupon schema definition
const CouponSchema: Schema<ICoupon, ICouponModel> = new Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
      index: true
    },
    name: {
      type: String,
      required: true,
      maxlength: 100
    },
    description: {
      type: String,
      required: true,
      maxlength: 500
    },
    type: {
      type: String,
      enum: CouponTypeValues,
      required: true,
      index: true
    },
    discountTarget: {
      type: String,
      enum: DiscountTargetValues,
      required: true,
      index: true
    },
    discountValue: {
      type: Number,
      required: true,
      min: 0
    },
    minimumOrderValue: {
      type: Number,
      min: 0,
      default: 0
    },
    maximumDiscountAmount: {
      type: Number,
      min: 0
    },
    usageLimit: {
      type: Number,
      required: true,
      min: 1,
      default: 1
    },
    usageCount: {
      type: Number,
      default: 0,
      min: 0
    },
    userUsageLimit: {
      type: Number,
      required: true,
      min: 1,
      default: 1
    },
    validFrom: {
      type: Date,
      required: true,
      index: true
    },
    validUntil: {
      type: Date,
      required: true,
      index: true
    },
    status: {
      type: String,
      enum: CouponStatusValues,
      default: 'active',
      index: true
    },
    isPublic: {
      type: Boolean,
      default: true,
      index: true
    },
    applicableProducts: [{
      type: Schema.Types.ObjectId,
      ref: 'Product'
    }],
    applicableCategories: [{
      type: Schema.Types.ObjectId,
      ref: 'Category'
    }],
    excludedProducts: [{
      type: Schema.Types.ObjectId,
      ref: 'Product'
    }],
    excludedCategories: [{
      type: Schema.Types.ObjectId,
      ref: 'Category'
    }],
    groupOrdersOnly: {
      type: Boolean,
      default: false,
      index: true
    },
    minimumGroupSize: {
      type: Number,
      min: 2
    },
    stackable: {
      type: Boolean,
      default: false
    },
    priority: {
      type: Number,
      default: 0,
      index: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for checking if coupon is expired
CouponSchema.virtual('isExpired').get(function() {
  return this.validUntil < new Date();
});

// Virtual for checking if coupon is used up
CouponSchema.virtual('isUsedUp').get(function() {
  return this.usageCount >= this.usageLimit;
});

// Virtual for checking if coupon is currently valid
CouponSchema.virtual('isCurrentlyValid').get(function() {
  const now = new Date();
  return this.status === 'active' && 
         this.validFrom <= now && 
         this.validUntil >= now && 
         !this.isUsedUp;
});

// Virtual for usage percentage
CouponSchema.virtual('usagePercentage').get(function() {
  return this.usageLimit > 0 ? (this.usageCount / this.usageLimit) * 100 : 0;
});

// Pre-save middleware
CouponSchema.pre('save', function(next) {
  // Auto-update status based on conditions
  const now = new Date();
  
  if (this.validUntil < now) {
    this.status = 'expired';
  } else if (this.usageCount >= this.usageLimit) {
    this.status = 'used_up';
  } else if (this.validFrom > now) {
    this.status = 'scheduled';
  } else if (this.status === 'scheduled' && this.validFrom <= now) {
    this.status = 'active';
  }

  // Validate group order settings
  if (this.groupOrdersOnly && !this.minimumGroupSize) {
    this.minimumGroupSize = 2;
  }

  next();
});

// Static methods
CouponSchema.statics.generateUniqueCode = async function(
  prefix: string = 'SAVE',
  length: number = 8
): Promise<string> {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    let code = prefix;
    for (let i = 0; i < length; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    const existing = await this.findOne({ code });
    if (!existing) {
      return code;
    }
    attempts++;
  }

  throw new Error('Unable to generate unique coupon code');
};

CouponSchema.statics.findValidCoupons = async function(filters: any = {}) {
  const now = new Date();
  
  const query = {
    status: 'active',
    validFrom: { $lte: now },
    validUntil: { $gte: now },
    $expr: { $lt: ['$usageCount', '$usageLimit'] },
    ...filters
  };

  return this.find(query)
    .populate('applicableProducts', 'name category')
    .populate('applicableCategories', 'name')
    .populate('excludedProducts', 'name category')
    .populate('excludedCategories', 'name')
    .sort({ priority: -1, createdAt: -1 });
};

CouponSchema.statics.validateCoupon = async function(
  code: string,
  userId: string,
  orderData: any
) {
  try {
    const coupon = await this.findOne({ 
      code: code.toUpperCase(),
      status: 'active'
    }).populate('applicableProducts applicableCategories excludedProducts excludedCategories');

    if (!coupon) {
      return { valid: false, error: 'Coupon not found or inactive' };
    }

    // Check if coupon is currently valid
    if (!coupon.isCurrentlyValid) {
      if (coupon.isExpired) {
        return { valid: false, error: 'Coupon has expired' };
      }
      if (coupon.isUsedUp) {
        return { valid: false, error: 'Coupon usage limit reached' };
      }
      return { valid: false, error: 'Coupon is not currently valid' };
    }

    // Check user usage limit
    const userUsageCount = await mongoose.model('CouponUsage').countDocuments({
      couponId: coupon._id,
      userId: new mongoose.Types.ObjectId(userId)
    });

    if (userUsageCount >= coupon.userUsageLimit) {
      return { valid: false, error: 'You have reached the usage limit for this coupon' };
    }

    // Check minimum order value
    if (coupon.minimumOrderValue && orderData.orderTotal < coupon.minimumOrderValue) {
      return { 
        valid: false, 
        error: `Minimum order value of ${coupon.minimumOrderValue} required` 
      };
    }

    // Check group order requirements
    if (coupon.groupOrdersOnly && !orderData.isGroupOrder) {
      return { valid: false, error: 'This coupon is only valid for group orders' };
    }

    if (coupon.minimumGroupSize && orderData.groupSize < coupon.minimumGroupSize) {
      return { 
        valid: false, 
        error: `Minimum group size of ${coupon.minimumGroupSize} required` 
      };
    }

    // Check product/category restrictions
    if (coupon.applicableProducts?.length > 0 || coupon.applicableCategories?.length > 0) {
      const hasApplicableItems = orderData.cartItems.some((item: any) => {
        const productMatch = coupon.applicableProducts?.some(
          (p: any) => p._id.toString() === item.productId
        );
        const categoryMatch = coupon.applicableCategories?.some(
          (c: any) => c._id.toString() === item.category
        );
        return productMatch || categoryMatch;
      });

      if (!hasApplicableItems) {
        return { valid: false, error: 'Coupon not applicable to items in cart' };
      }
    }

    // Check excluded products/categories
    if (coupon.excludedProducts?.length > 0 || coupon.excludedCategories?.length > 0) {
      const hasExcludedItems = orderData.cartItems.some((item: any) => {
        const productMatch = coupon.excludedProducts?.some(
          (p: any) => p._id.toString() === item.productId
        );
        const categoryMatch = coupon.excludedCategories?.some(
          (c: any) => c._id.toString() === item.category
        );
        return productMatch || categoryMatch;
      });

      if (hasExcludedItems) {
        return { valid: false, error: 'Coupon cannot be applied to some items in cart' };
      }
    }

    // Calculate discount
    const discountAmount = this.calculateDiscount(coupon, orderData);

    return {
      valid: true,
      coupon,
      discountAmount
    };
  } catch (error) {
    console.error('Error validating coupon:', error);
    return { valid: false, error: 'Error validating coupon' };
  }
};

CouponSchema.statics.calculateDiscount = function(coupon: ICoupon, orderData: any): number {
  let discountAmount = 0;

  switch (coupon.type) {
    case 'percentage':
      discountAmount = (orderData.orderTotal * coupon.discountValue) / 100;
      break;
    
    case 'fixed_amount':
      discountAmount = coupon.discountValue;
      break;
    
    case 'free_shipping':
      discountAmount = orderData.shippingCost || 0;
      break;
    
    case 'buy_x_get_y':
      // Implementation for buy X get Y logic
      // This would require additional configuration in the coupon
      discountAmount = 0; // Placeholder
      break;
    
    case 'bulk_discount':
      // Implementation for bulk discount logic
      const totalQuantity = orderData.cartItems.reduce(
        (sum: number, item: any) => sum + item.quantity, 0
      );
      if (totalQuantity >= coupon.discountValue) {
        discountAmount = orderData.orderTotal * 0.1; // 10% bulk discount
      }
      break;
    
    default:
      discountAmount = 0;
  }

  // Apply maximum discount limit
  if (coupon.maximumDiscountAmount) {
    discountAmount = Math.min(discountAmount, coupon.maximumDiscountAmount);
  }

  // Ensure discount doesn't exceed order total
  discountAmount = Math.min(discountAmount, orderData.orderTotal);

  return Math.round(discountAmount * 100) / 100; // Round to 2 decimal places
};

CouponSchema.statics.incrementUsage = async function(couponId: string, userId: string) {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const coupon = await this.findByIdAndUpdate(
      couponId,
      { $inc: { usageCount: 1 } },
      { new: true, session }
    );

    if (!coupon) {
      throw new Error('Coupon not found');
    }

    await session.commitTransaction();
    return coupon;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
};

CouponSchema.statics.getUsageStats = async function(couponId: string) {
  const coupon = await this.findById(couponId);
  if (!coupon) {
    throw new Error('Coupon not found');
  }

  const CouponUsage = mongoose.model('CouponUsage');
  const usageStats = await CouponUsage.aggregate([
    { $match: { couponId: new mongoose.Types.ObjectId(couponId) } },
    {
      $group: {
        _id: null,
        totalUsage: { $sum: 1 },
        totalDiscount: { $sum: '$discountAmount' },
        totalRevenue: { $sum: '$orderTotal' },
        averageDiscount: { $avg: '$discountAmount' },
        averageOrderValue: { $avg: '$orderTotal' }
      }
    }
  ]);

  return {
    coupon,
    stats: usageStats[0] || {
      totalUsage: 0,
      totalDiscount: 0,
      totalRevenue: 0,
      averageDiscount: 0,
      averageOrderValue: 0
    }
  };
};

CouponSchema.statics.cleanupExpired = async function() {
  const result = await this.updateMany(
    { 
      validUntil: { $lt: new Date() },
      status: { $ne: 'expired' }
    },
    { status: 'expired' }
  );

  return result.modifiedCount;
};

// Indexes for performance
CouponSchema.index({ code: 1 });
CouponSchema.index({ status: 1, validFrom: 1, validUntil: 1 });
CouponSchema.index({ type: 1, discountTarget: 1 });
CouponSchema.index({ isPublic: 1, status: 1 });
CouponSchema.index({ groupOrdersOnly: 1 });
CouponSchema.index({ priority: -1, createdAt: -1 });
CouponSchema.index({ createdBy: 1, createdAt: -1 });

// Prevent recompiling the model
let CouponModel: ICouponModel;

try {
  CouponModel = mongoose.model<ICoupon, ICouponModel>('Coupon');
} catch {
  CouponModel = mongoose.model<ICoupon, ICouponModel>('Coupon', CouponSchema);
}

export const Coupon = CouponModel;
