import bcrypt from 'bcryptjs';
import mongoose, { Schema, Document, model } from 'mongoose';
import crypto from 'crypto';

// Define User Roles
export type UserRole = 'admin' | 'member' | 'customer' | 'guest' ;

// Define Bank Details Interface
export interface BankDetails {
  accountName: string;
  accountNumber: string;
  bankName: string;
}

// Define IUser Interface
export interface IUser extends Document {
  createdAt: Date;
  updatedAt: Date;
  email: string;
  password: string;
  name: string;
  phone?: string;
  role: UserRole;
  refreshTokenHash?: string;
  refreshTokenExpiry?: Date;
  referrerId?: mongoose.Types.ObjectId | IUser;
  referralCode: string;
  bankDetails?: BankDetails;
  resetCode?: string;
  resetCodeExpires?: Date;
  tokenVersion: number;
  rememberMe: boolean;
  stokvelGroups: mongoose.Types.ObjectId[];
  comparePassword(candidatePassword: string): Promise<boolean>;
}

// Define User Schema
const UserSchema: Schema<IUser> = new Schema(
  {
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    name: { type: String, required: true },
    phone: { type: String, required: false },
    role: {
      type: String,
      required: true,
      enum: ['admin', 'member', 'customer', 'guest'],
      default: 'customer',
    },
    refreshTokenHash: { type: String, default: null },
    refreshTokenExpiry: { type: Date, default: null },
    referrerId: { type: Schema.Types.ObjectId, ref: 'User' },
    referralCode: { type: String, unique: true, required: true },
    bankDetails: {
      accountName: { type: String, required: false },
      accountNumber: { type: String, required: false },
      bankName: { type: String, required: false },
    },
    resetCode: { type: String, default: null },
    resetCodeExpires: { type: Date, default: null },
    tokenVersion: { type: Number, default: 0 },
    rememberMe: { type: Boolean, default: false },
    stokvelGroups: [{ type: Schema.Types.ObjectId, ref: 'StokvelGroup' }],
  },
  { timestamps: true }
);

// Middleware to hash passwords and generate referral codes
UserSchema.pre<IUser>('validate', async function (next) {
  if (this.isModified('password') || this.isNew) {
    this.password = await bcrypt.hash(this.password, 12);
  }

  if (!this.referralCode) {
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const MAX_ATTEMPTS = 5;

    while (!isUnique && attempts < MAX_ATTEMPTS) {
      code = crypto.randomBytes(3).toString('hex').toUpperCase();
      const existingUser = await mongoose.models.User.exists({ referralCode: code });
      if (!existingUser) {
        isUnique = true;
        this.referralCode = code;
      }
      attempts++;
    }

    if (!isUnique) {
      throw new Error('Failed to generate a unique referral code. Please try again.');
    }
  }

  next();
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  const isMatch = await bcrypt.compare(candidatePassword, this.password);
  if (!isMatch) {
    console.error(`Password comparison failed for user: ${this.email}`);
  }
  return isMatch;
};

// Ensure indexes for efficient queries (email and referralCode already have unique indexes from schema)
UserSchema.index({ referrerId: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ resetCode: 1 });
UserSchema.index({ resetCodeExpires: 1 });
UserSchema.index({ refreshTokenHash: 1 });

// Export User Model
export const User = mongoose.models.User || model<IUser>('User', UserSchema);


// import bcrypt from 'bcryptjs';
// import mongoose, { Schema, Document, model } from 'mongoose';
// import crypto from 'crypto';
// import '../lib/dbconnect';

// // Define User Roles
// export type UserRole = 'admin' | 'member' | 'customer' | 'guest';

// // Define Bank Details Interface
// export interface BankDetails {
//   accountName: string;
//   accountNumber: string;
//   bankName: string;
// }

// // Define IUser Interface
// export interface IUser extends Document {
//   createdAt: Date;
//   updatedAt: Date;
//   email: string;
//   password: string;
//   name: string;
//   phone?: string;
//   role: UserRole;
//   refreshTokenHash?: string;
//   refreshTokenExpiry?: Date;
//   referrerId?: mongoose.Types.ObjectId | IUser;
//   referralCode: string;
//   bankDetails?: BankDetails;
//   resetCode?: string;
//   resetCodeExpires?: Date;
//   tokenVersion: number;
//   rememberMe: boolean;
//   stokvelGroups: mongoose.Types.ObjectId[];
//   comparePassword(candidatePassword: string): Promise<boolean>;
// }

// // Define User Schema
// const UserSchema: Schema<IUser> = new Schema(
//   {
//     email: { 
//       type: String, 
//       required: true, 
//       unique: true
//     },
//     password: { 
//       type: String, 
//       required: true 
//     },
//     name: { 
//       type: String, 
//       required: true 
//     },
//     phone: { 
//       type: String, 
//       required: false 
//     },
//     role: {
//       type: String,
//       required: true,
//       enum: ['admin', 'member', 'customer', 'guest'],
//       default: 'customer'
//     },
//     refreshTokenHash: { 
//       type: String, 
//       default: null 
//     },
//     refreshTokenExpiry: { 
//       type: Date, 
//       default: null 
//     },
//     referrerId: { 
//       type: Schema.Types.ObjectId, 
//       ref: 'User' 
//     },
//     referralCode: { 
//       type: String, 
//       unique: true, 
//       required: true 
//     },
//     bankDetails: {
//       accountName: { type: String, required: false },
//       accountNumber: { type: String, required: false },
//       bankName: { type: String, required: false }
//     },
//     resetCode: { 
//       type: String, 
//       default: null 
//     },
//     resetCodeExpires: { 
//       type: Date, 
//       default: null 
//     },
//     tokenVersion: { 
//       type: Number, 
//       default: 0 
//     },
//     rememberMe: { 
//       type: Boolean, 
//       default: false 
//     },
//     stokvelGroups: [{ 
//       type: Schema.Types.ObjectId, 
//       ref: 'StokvelGroup' 
//     }]
//   },
//   { 
//     timestamps: true,
//     // Define all indexes in the schema options
//     indexes: [
//       { fields: { email: 1 }, options: { unique: true } },
//       { fields: { referralCode: 1 }, options: { unique: true } },
//       { fields: { referrerId: 1 } },
//       { fields: { role: 1 } },
//       { fields: { createdAt: -1 } },
//       { fields: { resetCode: 1 } },
//       { fields: { resetCodeExpires: 1 } },
//       { fields: { refreshTokenHash: 1 } }
//     ]
//   }
// );

// // Middleware to hash passwords and generate referral codes
// UserSchema.pre<IUser>('validate', async function (next) {
//   if (this.isModified('password') || this.isNew) {
//     this.password = await bcrypt.hash(this.password, 12);
//   }

//   if (!this.referralCode) {
//     let code: string;
//     let isUnique = false;
//     let attempts = 0;
//     const MAX_ATTEMPTS = 5;

//     while (!isUnique && attempts < MAX_ATTEMPTS) {
//       code = crypto.randomBytes(3).toString('hex').toUpperCase();
//       const UserModel = mongoose.models?.User || mongoose.model<IUser>('User', UserSchema);
//       const existingUser = await UserModel.exists({ referralCode: code });
//       if (!existingUser) {
//         isUnique = true;
//         this.referralCode = code;
//       }
//       attempts++;
//     }

//     if (!isUnique) {
//       throw new Error('Failed to generate a unique referral code. Please try again.');
//     }
//   }

//   next();
// });

// // Method to compare passwords
// UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
//   const isMatch = await bcrypt.compare(candidatePassword, this.password);
//   if (!isMatch) {
//     console.error(`Password comparison failed for user: ${this.email}`);
//   }
//   return isMatch;
// };

// // Export User Model
// const UserModel = (mongoose.models?.User || mongoose.model<IUser>('User', UserSchema)) as mongoose.Model<IUser>;

// export const User = UserModel;
// export type UserModel = typeof UserModel;