// // models/index.ts
// import './User';
// import './StokvelGroup';
// import './Product';
// import './ProductCategory';
// import './ShoppingCart';
// import './Referral';

// export { User } from './User';
// export { StokvelGroup } from './StokvelGroup';
// export { Product } from './Product';
// export { ProductCategory } from './ProductCategory';
// export { ShoppingCart } from './ShoppingCart';
// export { Referral } from './Referral';



// models/index.ts
import '../lib/dbconnect';

// Import all models
// import './BaseSubscription';
import './Delivery';
import './FuneralBenefits';
import './GrocerySchoolBundle';
import './GroupOrder';
import './MemberOrder';
import './LoanApplication';
import './MonthlyGroceries';
import './Product';
import './ProductArchive';
import './ProductCategory';
import './ProductCategoryArchive';
import './ProductRating';
import './Referral';
import './ShoppingCart';
import './StokvelGroup';
import './User';
import './Wishlist';
import './YearEndBundle';

// Export all models and their types
// export * from './BaseSubscription';
export * from './Delivery';
export * from './FuneralBenefits';
export * from './GrocerySchoolBundle';
export * from './GroupOrder';
export * from './MemberOrder';
export * from './LoanApplication';
export * from './MonthlyGroceries';
export * from './Product';
export * from './ProductArchive';
export * from './ProductCategory';
export * from './ProductCategoryArchive';
export * from './ProductRating';
export * from './Referral';
export * from './ShoppingCart';
export * from './StokvelGroup';
export * from './User';
export * from './Wishlist';
export * from './YearEndBundle';