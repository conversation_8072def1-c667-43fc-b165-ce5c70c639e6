// models/OrderFulfillment.ts
import mongoose, { Schema, Document, Model } from 'mongoose';
import { 
  FulfillmentStatus, 
  ShippingProvider, 
  OrderPriority,
  IOrderFulfillment 
} from '@/types/orderFulfillment';

// Fulfillment status enum values
const FulfillmentStatusValues = [
  'pending',
  'confirmed',
  'processing',
  'packed',
  'shipped',
  'out_for_delivery',
  'delivered',
  'failed',
  'cancelled',
  'returned'
] as const;

// Shipping provider enum values
const ShippingProviderValues = [
  'courier_guy',
  'fastway',
  'dawn_wing',
  'aramex',
  'pudo',
  'self_collection'
] as const;

// Order priority enum values
const OrderPriorityValues = [
  'low',
  'normal',
  'high',
  'urgent'
] as const;

// Fulfillment step schema
const FulfillmentStepSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  status: { 
    type: String, 
    enum: FulfillmentStatusValues,
    required: true 
  },
  description: { type: String, required: true },
  completedAt: { type: Date },
  estimatedCompletion: { type: Date },
  notes: { type: String },
  assignedTo: { type: String },
  duration: { type: Number } // in minutes
}, { _id: false });

// Shipping address schema
const ShippingAddressSchema = new Schema({
  street: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String, required: true },
  postalCode: { type: String, required: true },
  country: { type: String, required: true, default: 'South Africa' },
  instructions: { type: String }
}, { _id: false });

// Package dimensions schema
const PackageDimensionsSchema = new Schema({
  length: { type: Number, required: true },
  width: { type: Number, required: true },
  height: { type: Number, required: true },
  weight: { type: Number, required: true }
}, { _id: false });

// Shipping info schema
const ShippingInfoSchema = new Schema({
  provider: { 
    type: String, 
    enum: ShippingProviderValues,
    required: true 
  },
  trackingNumber: { type: String },
  trackingUrl: { type: String },
  estimatedDelivery: { type: Date },
  actualDelivery: { type: Date },
  shippingCost: { type: Number, required: true, min: 0 },
  shippingAddress: { type: ShippingAddressSchema, required: true },
  packageDimensions: { type: PackageDimensionsSchema }
}, { _id: false });

// Inventory allocation schema
const InventoryAllocationSchema = new Schema({
  productId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Product',
    required: true 
  },
  quantity: { type: Number, required: true, min: 1 },
  allocatedFrom: { type: String, required: true },
  reservedAt: { type: Date, required: true, default: Date.now },
  expiresAt: { 
    type: Date, 
    required: true,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
  }
}, { _id: false });

// Interface for model methods and statics
interface IOrderFulfillmentModel extends Model<IOrderFulfillment> {
  findByOrderId(orderId: string): Promise<IOrderFulfillment | null>;
  findByStatus(status: FulfillmentStatus): Promise<IOrderFulfillment[]>;
  findByWarehouse(warehouse: string): Promise<IOrderFulfillment[]>;
  updateStatus(fulfillmentId: string, status: FulfillmentStatus, notes?: string): Promise<IOrderFulfillment | null>;
  getFulfillmentAnalytics(startDate?: Date, endDate?: Date): Promise<any>;
}

// Order fulfillment schema definition
const OrderFulfillmentSchema: Schema<IOrderFulfillment, IOrderFulfillmentModel> = new Schema(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'GroupOrder',
      required: true,
      unique: true,
      index: true
    },
    groupOrderId: {
      type: Schema.Types.ObjectId,
      ref: 'GroupOrder',
      index: true
    },
    status: {
      type: String,
      enum: FulfillmentStatusValues,
      default: 'pending',
      required: true,
      index: true
    },
    priority: {
      type: String,
      enum: OrderPriorityValues,
      default: 'normal',
      required: true,
      index: true
    },
    steps: [FulfillmentStepSchema],
    shippingInfo: ShippingInfoSchema,
    inventoryAllocations: [InventoryAllocationSchema],
    fulfillmentNotes: [{
      type: String,
      maxlength: 1000
    }],
    estimatedFulfillmentDate: {
      type: Date,
      index: true
    },
    actualFulfillmentDate: {
      type: Date,
      index: true
    },
    assignedWarehouse: {
      type: String,
      index: true
    },
    assignedStaff: [{
      type: String
    }]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for checking if order is completed
OrderFulfillmentSchema.virtual('isCompleted').get(function() {
  return ['delivered', 'cancelled', 'returned'].includes(this.status);
});

// Virtual for checking if order is in progress
OrderFulfillmentSchema.virtual('isInProgress').get(function() {
  return ['confirmed', 'processing', 'packed', 'shipped', 'out_for_delivery'].includes(this.status);
});

// Virtual for calculating fulfillment duration
OrderFulfillmentSchema.virtual('fulfillmentDuration').get(function() {
  if (this.actualFulfillmentDate && this.createdAt) {
    return Math.round((this.actualFulfillmentDate.getTime() - this.createdAt.getTime()) / (1000 * 60 * 60)); // hours
  }
  return null;
});

// Virtual for current step
OrderFulfillmentSchema.virtual('currentStep').get(function() {
  return this.steps.find(step => step.status === this.status) || null;
});

// Pre-save middleware to update step statuses
OrderFulfillmentSchema.pre('save', function(next) {
  // Update the current step when status changes
  if (this.isModified('status')) {
    const currentStep = this.steps.find(step => step.status === this.status);
    if (currentStep && !currentStep.completedAt) {
      currentStep.completedAt = new Date();
    }
    
    // Set actual fulfillment date when delivered
    if (this.status === 'delivered' && !this.actualFulfillmentDate) {
      this.actualFulfillmentDate = new Date();
    }
  }
  next();
});

// Static methods
OrderFulfillmentSchema.statics.findByOrderId = async function(orderId: string) {
  return this.findOne({ orderId: new mongoose.Types.ObjectId(orderId) })
    .populate('orderId')
    .populate('inventoryAllocations.productId', 'name sku price');
};

OrderFulfillmentSchema.statics.findByStatus = async function(status: FulfillmentStatus) {
  return this.find({ status })
    .populate('orderId')
    .sort({ createdAt: -1 });
};

OrderFulfillmentSchema.statics.findByWarehouse = async function(warehouse: string) {
  return this.find({ assignedWarehouse: warehouse })
    .populate('orderId')
    .sort({ priority: -1, createdAt: 1 });
};

OrderFulfillmentSchema.statics.updateStatus = async function(
  fulfillmentId: string, 
  status: FulfillmentStatus, 
  notes?: string
) {
  const update: any = { 
    status,
    updatedAt: new Date()
  };
  
  if (notes) {
    update.$push = { fulfillmentNotes: notes };
  }
  
  return this.findByIdAndUpdate(fulfillmentId, update, { new: true });
};

OrderFulfillmentSchema.statics.getFulfillmentAnalytics = async function(startDate?: Date, endDate?: Date) {
  const matchStage: any = {};
  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }

  const analytics = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        averageFulfillmentTime: {
          $avg: {
            $cond: [
              { $and: [{ $ne: ['$actualFulfillmentDate', null] }, { $ne: ['$createdAt', null] }] },
              { $divide: [{ $subtract: ['$actualFulfillmentDate', '$createdAt'] }, 1000 * 60 * 60] },
              null
            ]
          }
        },
        onTimeDeliveries: {
          $sum: {
            $cond: [
              { 
                $and: [
                  { $ne: ['$actualFulfillmentDate', null] },
                  { $ne: ['$estimatedFulfillmentDate', null] },
                  { $lte: ['$actualFulfillmentDate', '$estimatedFulfillmentDate'] }
                ]
              },
              1,
              0
            ]
          }
        },
        totalDelivered: {
          $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
        }
      }
    }
  ]);

  const statusBreakdown = await this.aggregate([
    { $match: matchStage },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  const result = analytics[0] || {
    totalOrders: 0,
    averageFulfillmentTime: 0,
    onTimeDeliveries: 0,
    totalDelivered: 0
  };

  result.onTimeDeliveryRate = result.totalDelivered > 0 
    ? (result.onTimeDeliveries / result.totalDelivered) * 100 
    : 0;

  result.ordersByStatus = statusBreakdown.reduce((acc, item) => {
    acc[item._id] = item.count;
    return acc;
  }, {});

  return result;
};

// Indexes for performance
OrderFulfillmentSchema.index({ orderId: 1 }, { unique: true });
OrderFulfillmentSchema.index({ status: 1, createdAt: -1 });
OrderFulfillmentSchema.index({ assignedWarehouse: 1, status: 1 });
OrderFulfillmentSchema.index({ priority: -1, createdAt: 1 });
OrderFulfillmentSchema.index({ estimatedFulfillmentDate: 1 });
OrderFulfillmentSchema.index({ 'inventoryAllocations.expiresAt': 1 });

// Prevent recompiling the model
let OrderFulfillmentModel: IOrderFulfillmentModel;

try {
  OrderFulfillmentModel = mongoose.model<IOrderFulfillment, IOrderFulfillmentModel>('OrderFulfillment');
} catch {
  OrderFulfillmentModel = mongoose.model<IOrderFulfillment, IOrderFulfillmentModel>('OrderFulfillment', OrderFulfillmentSchema);
}

export const OrderFulfillment = OrderFulfillmentModel;
