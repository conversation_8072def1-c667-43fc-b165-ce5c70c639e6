// models/Wishlist.ts

import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IWishlistItem {
  product: mongoose.Types.ObjectId;
  addedAt: Date;
  notes?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface IWishlist extends Document {
  userId: mongoose.Types.ObjectId;
  items: IWishlistItem[];
  name: string;
  description?: string;
  isPublic: boolean;
  shareCode?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Wishlist Item Schema
const WishlistItemSchema: Schema = new Schema({
  product: { 
    type: Schema.Types.ObjectId, 
    ref: 'Product', 
    required: true,
    index: true
  },
  addedAt: { 
    type: Date, 
    default: Date.now 
  },
  notes: { 
    type: String, 
    maxlength: 500 
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  }
});

// Wishlist Schema
const WishlistSchema: Schema<IWishlist> = new Schema(
  {
    userId: { 
      type: Schema.Types.ObjectId, 
      ref: 'User', 
      required: true,
      index: true
    },
    items: [WishlistItemSchema],
    name: { 
      type: String, 
      required: true,
      maxlength: 100,
      default: 'My Wishlist'
    },
    description: { 
      type: String, 
      maxlength: 500 
    },
    isPublic: { 
      type: Boolean, 
      default: false 
    },
    shareCode: { 
      type: String, 
      unique: true,
      sparse: true,
      index: true
    },
    tags: [{ 
      type: String, 
      maxlength: 50 
    }]
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for efficient queries
WishlistSchema.index({ userId: 1, createdAt: -1 });
WishlistSchema.index({ userId: 1, 'items.product': 1 });
WishlistSchema.index({ shareCode: 1 });
WishlistSchema.index({ isPublic: 1, createdAt: -1 });
WishlistSchema.index({ tags: 1 });

// Virtual for item count
WishlistSchema.virtual('itemCount').get(function() {
  return this.items.length;
});

// Virtual for total value (requires population)
WishlistSchema.virtual('totalValue').get(function() {
  if (!this.populated('items.product')) return 0;
  return this.items.reduce((total, item) => {
    const product = item.product as any;
    return total + (product?.price || 0);
  }, 0);
});

// Pre-save middleware to generate share code
WishlistSchema.pre('save', function(next) {
  if (this.isPublic && !this.shareCode) {
    this.shareCode = generateShareCode();
  } else if (!this.isPublic) {
    this.shareCode = undefined;
  }
  next();
});

// Static methods
WishlistSchema.statics.findByUser = function(userId: string) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

WishlistSchema.statics.findByShareCode = function(shareCode: string) {
  return this.findOne({ shareCode, isPublic: true });
};

WishlistSchema.statics.findPublicWishlists = function(limit: number = 10) {
  return this.find({ isPublic: true })
    .populate('userId', 'name')
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Instance methods
WishlistSchema.methods.addItem = function(productId: string, notes?: string, priority: 'low' | 'medium' | 'high' = 'medium') {
  // Check if item already exists
  const existingItem = this.items.find(
    (item: IWishlistItem) => item.product.toString() === productId
  );
  
  if (existingItem) {
    // Update existing item
    existingItem.notes = notes || existingItem.notes;
    existingItem.priority = priority;
    existingItem.addedAt = new Date();
  } else {
    // Add new item
    this.items.push({
      product: new mongoose.Types.ObjectId(productId),
      addedAt: new Date(),
      notes,
      priority
    });
  }
  
  return this.save();
};

WishlistSchema.methods.removeItem = function(productId: string) {
  this.items = this.items.filter(
    (item: IWishlistItem) => item.product.toString() !== productId
  );
  return this.save();
};

WishlistSchema.methods.updateItem = function(productId: string, updates: Partial<IWishlistItem>) {
  const item = this.items.find(
    (item: IWishlistItem) => item.product.toString() === productId
  );
  
  if (item) {
    Object.assign(item, updates);
    return this.save();
  }
  
  throw new Error('Item not found in wishlist');
};

WishlistSchema.methods.hasProduct = function(productId: string): boolean {
  return this.items.some(
    (item: IWishlistItem) => item.product.toString() === productId
  );
};

WishlistSchema.methods.getItemsByPriority = function(priority: 'low' | 'medium' | 'high') {
  return this.items.filter((item: IWishlistItem) => item.priority === priority);
};

WishlistSchema.methods.clearWishlist = function() {
  this.items = [];
  return this.save();
};

WishlistSchema.methods.generateShareCode = function() {
  this.shareCode = generateShareCode();
  this.isPublic = true;
  return this.save();
};

// Helper function to generate share code
function generateShareCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Middleware to populate product details
WishlistSchema.pre(/^find/, function() {
  this.populate({
    path: 'items.product',
    select: 'name price image description stock category',
    populate: {
      path: 'category',
      select: 'name'
    }
  });
});

// Export the model
export const Wishlist = mongoose.models.Wishlist || mongoose.model<IWishlist>('Wishlist', WishlistSchema);

export type WishlistModel = typeof Wishlist;
