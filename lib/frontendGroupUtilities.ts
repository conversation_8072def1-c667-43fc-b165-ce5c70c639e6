// lib/frontendGroupUtilities.ts
"use client";

import { useQuery, useMutation, useQueryClient, UseMutationResult } from "@tanstack/react-query";
import { User } from "@/types/user";

export interface StokvelGroup {
  _id: string;
  name: string;
  description: string;
  admin: string;
  geolocation: string;
  members?: string[];
  totalSales: number;
  hasDelivery: boolean;
  avgOrderValue: number;
  activeOrders: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateGroupInput {
  name: string;
  description: string;
  admin: string;
  geolocation: string;
  members?: string[];
  totalSales?: number;
  hasDelivery: boolean;
  avgOrderValue?: number;
  activeOrders?: number;
}

export interface UpdateGroupInput {
  id: string;
  updateData: Partial<Omit<StokvelGroup, '_id' | 'createdAt' | 'updatedAt'>>;
}

export async function getAllStokvelGroups(): Promise<StokvelGroup[]> {
  const response = await fetch("/api/stokvel-groups/get-all", { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to fetch StokvelGroups");
  }
  return response.json();
}

export async function createStokvelGroup(data: CreateGroupInput): Promise<StokvelGroup> {
  const response = await fetch("/api/stokvel-groups/create", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to create StokvelGroup");
  }
  return response.json();
}

export async function updateStokvelGroup(input: UpdateGroupInput): Promise<StokvelGroup> {
  const response = await fetch("/api/stokvel-groups/update", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  });
  if (!response.ok) {
    throw new Error("Failed to update StokvelGroup");
  }
  return response.json();
}

export async function deleteStokvelGroup(id: string): Promise<void> {
  const response = await fetch("/api/stokvel-groups/delete", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  });
  if (!response.ok) {
    throw new Error("Failed to delete StokvelGroup");
  }
}

export function useGetAllStokvelGroups() {
  return useQuery<StokvelGroup[], Error>({
    queryKey: ["stokvelGroups"],
    queryFn: getAllStokvelGroups,
  });
}

export function useCreateStokvelGroup(): UseMutationResult<StokvelGroup, Error, CreateGroupInput, unknown> {
  const queryClient = useQueryClient();

  return useMutation<StokvelGroup, Error, CreateGroupInput, unknown>({
    mutationFn: createStokvelGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["stokvelGroups"] });
    },
  });
}

export function useUpdateStokvelGroup(): UseMutationResult<StokvelGroup, Error, UpdateGroupInput, unknown> {
  const queryClient = useQueryClient();

  return useMutation<StokvelGroup, Error, UpdateGroupInput, unknown>({
    mutationFn: updateStokvelGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["stokvelGroups"] });
    },
  });
}

export function useDeleteStokvelGroup(): UseMutationResult<void, Error, string, unknown> {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string, unknown>({
    mutationFn: deleteStokvelGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["stokvelGroups"] });
    },
  });
}




/**
 * 1) Check if a user by email exists in the DB
 * - Returns user object or null
 */
export async function checkUserByEmail(email: string): Promise<User | null> {
  try {
    const res = await fetch(`/api/users/find-by-email?email=${encodeURIComponent(email)}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
    });

    if (!res.ok) {
      if (res.status === 404) return null; // user not found
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to check user");
    }

    // If found, parse the user
    const data = await res.json();
    // e.g. { user: {...} }
    return data.user as User;
  } catch (error) {
    console.error("Error checking user by email:", error);
    throw error;
  }
}

/**
 * 2) Join a stokvel group
 * - Expects userId, groupId
 * - Example: /api/stokvel-groups/join
 */
export async function joinGroup(userId: string, groupId: string): Promise<StokvelGroup> {
  try {
    const res = await fetch("/api/stokvel-groups/join", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ userId, groupId }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to join group");
    }

    const data = await res.json();
    // e.g. { group: {...} }
    return data.group as StokvelGroup;
  } catch (error) {
    console.error("Error joining group:", error);
    throw error;
  }
}

/**
 * 3) Store a location request if no group is found
 * - Admin can later see these requests
 */
export async function storeLocationRequest(location: string): Promise<void> {
  try {
    const res = await fetch("/api/location-requests", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ location }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to store location request");
    }
    // success => do nothing
  } catch (error) {
    console.error("Error storing location request:", error);
    throw error;
  }
}