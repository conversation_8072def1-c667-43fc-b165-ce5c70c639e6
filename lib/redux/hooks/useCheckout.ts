import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  useCreateOrUpdateGroupOrderMutation,
  useCalculateGroupDiscountQuery
} from '@/lib/redux/features/cart/cartApiSlice';
import {
  selectCart,
  selectCartItems,
  selectSubtotal
} from '@/lib/redux/features/cart/cartSlice';
import { useAppSelector } from '@/lib/redux/hooks';
import { useCart } from './useCart';
import { CreateGroupOrderInput } from '@/types/unifiedCart';
import {
  PaymentMethodType,
  PaymentData,
  PaymentFormData,
  ProcessPaymentRequest,
  ProcessPaymentResponse
} from '@/types/payment';

export interface CheckoutFormData {
  name: string;
  email: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  paymentMethod?: PaymentMethodType;
  savePaymentInfo?: boolean;
  // Payment method specific fields
  cardNumber?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cvv?: string;
  cardholderName?: string;
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  branchCode?: string;
}

export function useCheckout(groupId: string) {
  const { user } = useAuth();
  const userId = user?._id || '';

  // Redux selectors
  const cart = useAppSelector(selectCart);
  const cartItems = useAppSelector(selectCartItems);
  const subtotal = useAppSelector(selectSubtotal);

  // Cart management hook
  const { clearCart } = useCart(groupId);

  // RTK Query hooks
  const [createOrder, { isLoading: isCreatingOrder, error: orderError }] =
    useCreateOrUpdateGroupOrderMutation();

  const { data: discountInfo, isLoading: isCalculatingDiscount } =
    useCalculateGroupDiscountQuery(groupId, { skip: !groupId });

  // Local state for checkout process
  const [checkoutStep, setCheckoutStep] = useState<'cart' | 'customer-info' | 'payment' | 'summary'>('cart');
  const [formData, setFormData] = useState<CheckoutFormData>({
    name: user?.name || '',
    email: user?.email || '',
    address: '',
    city: '',
    country: '',
    postalCode: '',
    paymentMethod: 'credit_card',
    savePaymentInfo: false
  });

  // Update form data
  const updateFormData = (data: Partial<CheckoutFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  // Navigate between checkout steps
  const goToStep = (step: 'cart' | 'customer-info' | 'payment' | 'summary') => {
    setCheckoutStep(step);
  };

  // Process payment
  const processPayment = async (paymentFormData: PaymentFormData) => {
    if (!userId || !groupId || !cart) return { success: false, error: 'Missing required data' };

    try {
      // Prepare payment data
      const paymentData: PaymentData = {
        orderId: '', // Will be set after order creation
        userId,
        amount: subtotal,
        currency: 'ZAR',
        paymentMethod: paymentFormData.paymentMethod,
        provider: getPaymentProvider(paymentFormData.paymentMethod),
        description: `Group order payment for ${cart.items.length} items`,
        metadata: {
          groupId,
          cartId: cart._id,
          itemCount: cart.items.length
        }
      };

      // Add payment method specific data
      if (paymentFormData.paymentMethod === 'credit_card') {
        paymentData.creditCard = {
          cardNumber: paymentFormData.cardNumber || '',
          expiryMonth: paymentFormData.expiryMonth || '',
          expiryYear: paymentFormData.expiryYear || '',
          cvv: paymentFormData.cvv || '',
          cardholderName: paymentFormData.cardholderName || ''
        };
      } else if (paymentFormData.paymentMethod === 'bank_transfer') {
        paymentData.bankTransfer = {
          bankName: paymentFormData.bankName || '',
          accountNumber: paymentFormData.accountNumber || '',
          accountHolderName: paymentFormData.accountHolderName || ''
        };
      } else if (paymentFormData.paymentMethod === 'eft') {
        paymentData.eft = {
          bankName: paymentFormData.bankName || '',
          accountNumber: paymentFormData.accountNumber || '',
          branchCode: paymentFormData.branchCode || '',
          accountHolderName: paymentFormData.accountHolderName || ''
        };
      }

      // First create the order
      const orderData: CreateGroupOrderInput = {
        userId,
        groupId,
        customerInfo: {
          name: formData.name,
          email: formData.email,
          address: formData.address,
          city: formData.city,
          country: formData.country,
          postalCode: formData.postalCode
        },
        paymentMethod: paymentFormData.paymentMethod
      };

      const orderResult = await createOrder(orderData).unwrap();

      // Clear the cart after successful order creation
      await clearCart();

      // Return success with actual order ID
      return {
        success: true,
        orderId: orderResult?.orderId || orderResult?.memberOrderId || 'order_created'
      };

    } catch (error) {
      console.error('Error processing payment:', error);
      return { success: false, error: 'Payment processing failed' };
    }
  };

  // Submit order (legacy method for backward compatibility)
  const submitOrder = async () => {
    if (!userId || !groupId) return false;

    const orderData: CreateGroupOrderInput = {
      userId,
      groupId,
      customerInfo: {
        name: formData.name,
        email: formData.email,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        postalCode: formData.postalCode
      },
      paymentMethod: formData.paymentMethod || 'credit_card'
    };

    try {
      await createOrder(orderData);
      // Clear the cart after successful order creation
      await clearCart();
      return true;
    } catch (error) {
      console.error('Error submitting order:', error);
      return false;
    }
  };

  return {
    // Data
    cart,
    cartItems,
    subtotal,
    discountInfo,
    formData,
    checkoutStep,

    // Loading states
    isCreatingOrder,
    isCalculatingDiscount,

    // Error states
    orderError,

    // Actions
    updateFormData,
    goToStep,
    submitOrder,
    processPayment,
    clearCart
  };
}

// Helper function to get payment provider based on payment method
function getPaymentProvider(paymentMethod: PaymentMethodType): string {
  switch (paymentMethod) {
    case 'credit_card':
      return 'stripe';
    case 'bank_transfer':
      return 'payfast';
    case 'eft':
      return 'ozow';
    case 'paypal':
      return 'paypal';
    default:
      return 'stripe';
  }
}
