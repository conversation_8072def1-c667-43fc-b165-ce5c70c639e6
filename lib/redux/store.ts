// lib/redux/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { groupsApi } from './features/groups/groupsApiSlice';

// Payment Module Reducers
import { payFastReducer } from '@/modules/payments/payfast';
import { peachReducer } from '@/modules/payments/peach';
import { codReducer } from '@/modules/payments/cod';

export const store = configureStore({
  reducer: {
    [groupsApi.reducerPath]: groupsApi.reducer,
    // Payment Modules
    payfast: payFastReducer,
    peach: peachReducer,
    cod: codReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(groupsApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
