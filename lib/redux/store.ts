// lib/redux/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { groupsApi } from './features/groups/groupsApiSlice';

// Payment Module Reducers (using direct imports to avoid client/server issues)
import payFastSlice from '@/modules/payments/payfast/store/payFastSlice';
import peachSlice from '@/modules/payments/peach/store/peachSlice';
import codSlice from '@/modules/payments/cod/store/codSlice';

export const store = configureStore({
  reducer: {
    [groupsApi.reducerPath]: groupsApi.reducer,
    // Payment Modules
    payfast: payFastSlice,
    peach: peachSlice,
    cod: codSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(groupsApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
