// Payment System Configuration

export interface PaymentConfig {
  payfast: {
    enabled: boolean;
    merchantId: string;
    merchantKey: string;
    passphrase?: string;
    sandbox: boolean;
    returnUrl: string;
    cancelUrl: string;
    notifyUrl: string;
  };
  peach: {
    enabled: boolean;
    entityId: string;
    username: string;
    password: string;
    sandbox: boolean;
    baseUrl: string;
    successUrl: string;
    cancelUrl: string;
    webhookUrl?: string;
  };
  cod: {
    enabled: boolean;
    maxAmount: number;
    minAmount: number;
    deliveryFee: number;
    deliveryFeeType: 'fixed' | 'percentage';
    supportedAreas: string[];
    estimatedDeliveryDays: number;
  };
}

// Get payment configuration from environment variables
export const getPaymentConfig = (): PaymentConfig => {
  return {
    payfast: {
      enabled: process.env.PAYFAST_ENABLED === 'true',
      merchantId: process.env.PAYFAST_MERCHANT_ID || '',
      merchantKey: process.env.PAYFAST_MERCHANT_KEY || '',
      passphrase: process.env.PAYFAST_PASSPHRASE,
      sandbox: process.env.PAYFAST_SANDBOX === 'true',
      returnUrl: process.env.PAYFAST_RETURN_URL || '',
      cancelUrl: process.env.PAYFAST_CANCEL_URL || '',
      notifyUrl: process.env.PAYFAST_NOTIFY_URL || ''
    },
    peach: {
      enabled: process.env.PEACH_ENABLED === 'true',
      entityId: process.env.PEACH_ENTITY_ID || '',
      username: process.env.PEACH_USERNAME || '',
      password: process.env.PEACH_PASSWORD || '',
      sandbox: process.env.PEACH_SANDBOX === 'true',
      baseUrl: process.env.PEACH_BASE_URL || '',
      successUrl: process.env.PEACH_SUCCESS_URL || '',
      cancelUrl: process.env.PEACH_CANCEL_URL || '',
      webhookUrl: process.env.PEACH_WEBHOOK_URL
    },
    cod: {
      enabled: process.env.COD_ENABLED === 'true',
      maxAmount: parseInt(process.env.COD_MAX_AMOUNT || '5000'),
      minAmount: parseInt(process.env.COD_MIN_AMOUNT || '50'),
      deliveryFee: parseInt(process.env.COD_DELIVERY_FEE || '50'),
      deliveryFeeType: (process.env.COD_DELIVERY_FEE_TYPE as 'fixed' | 'percentage') || 'fixed',
      supportedAreas: process.env.COD_SUPPORTED_AREAS?.split(',') || [
        'Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'
      ],
      estimatedDeliveryDays: parseInt(process.env.COD_ESTIMATED_DELIVERY_DAYS || '3')
    }
  };
};

// Get available payment methods based on configuration
export const getAvailablePaymentMethods = () => {
  const config = getPaymentConfig();
  const methods = [];

  if (config.payfast.enabled) {
    methods.push('payfast');
  }

  if (config.peach.enabled) {
    methods.push('peach');
  }

  if (config.cod.enabled) {
    methods.push('cod');
  }

  return methods;
};

// Validate payment configuration
export const validatePaymentConfig = (): { isValid: boolean; errors: string[] } => {
  const config = getPaymentConfig();
  const errors: string[] = [];

  // Validate PayFast
  if (config.payfast.enabled) {
    if (!config.payfast.merchantId) errors.push('PayFast Merchant ID is required');
    if (!config.payfast.merchantKey) errors.push('PayFast Merchant Key is required');
    if (!config.payfast.returnUrl) errors.push('PayFast Return URL is required');
    if (!config.payfast.cancelUrl) errors.push('PayFast Cancel URL is required');
    if (!config.payfast.notifyUrl) errors.push('PayFast Notify URL is required');
  }

  // Validate Peach
  if (config.peach.enabled) {
    if (!config.peach.entityId) errors.push('Peach Entity ID is required');
    if (!config.peach.username) errors.push('Peach Username is required');
    if (!config.peach.password) errors.push('Peach Password is required');
    if (!config.peach.baseUrl) errors.push('Peach Base URL is required');
    if (!config.peach.successUrl) errors.push('Peach Success URL is required');
    if (!config.peach.cancelUrl) errors.push('Peach Cancel URL is required');
  }

  // Validate COD
  if (config.cod.enabled) {
    if (config.cod.maxAmount <= 0) errors.push('COD Max Amount must be greater than 0');
    if (config.cod.minAmount < 0) errors.push('COD Min Amount cannot be negative');
    if (config.cod.minAmount >= config.cod.maxAmount) errors.push('COD Min Amount must be less than Max Amount');
    if (config.cod.deliveryFee < 0) errors.push('COD Delivery Fee cannot be negative');
    if (config.cod.estimatedDeliveryDays <= 0) errors.push('COD Estimated Delivery Days must be greater than 0');
    if (!config.cod.supportedAreas.length) errors.push('COD must have at least one supported area');
  }

  // Check if at least one payment method is enabled
  if (!config.payfast.enabled && !config.peach.enabled && !config.cod.enabled) {
    errors.push('At least one payment method must be enabled');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Default environment variables template
export const getEnvTemplate = () => {
  return `
# Payment Configuration
# Enable/disable payment methods
PAYFAST_ENABLED=true
PEACH_ENABLED=true
COD_ENABLED=true

# PayFast Configuration
PAYFAST_MERCHANT_ID=10000100
PAYFAST_MERCHANT_KEY=46f0cd694581a
PAYFAST_PASSPHRASE=jt7NOE43FZPn
PAYFAST_SANDBOX=true
PAYFAST_RETURN_URL=http://localhost:3001/payment/success
PAYFAST_CANCEL_URL=http://localhost:3001/payment/cancel
PAYFAST_NOTIFY_URL=http://localhost:3001/api/payment/payfast/notify

# Peach Payments Configuration
PEACH_ENTITY_ID=8a8294174b7ecb28014b9699220015ca
PEACH_USERNAME=8a8294174b7ecb28014b9699220015ca
PEACH_PASSWORD=sy6KJsT8
PEACH_SANDBOX=true
PEACH_BASE_URL=https://testapi-v2.peachpayments.com
PEACH_SUCCESS_URL=http://localhost:3001/payment/success
PEACH_CANCEL_URL=http://localhost:3001/payment/cancel
PEACH_WEBHOOK_URL=http://localhost:3001/api/webhooks/peach

# Cash on Delivery Configuration
COD_ENABLED=true
COD_MAX_AMOUNT=5000
COD_MIN_AMOUNT=50
COD_DELIVERY_FEE=50
COD_DELIVERY_FEE_TYPE=fixed
COD_SUPPORTED_AREAS=Cape Town,Johannesburg,Durban,Pretoria,Port Elizabeth
COD_ESTIMATED_DELIVERY_DAYS=3
`;
};

// Payment method priorities (for default selection)
export const getPaymentMethodPriority = () => {
  return ['peach', 'payfast', 'cod'];
};

// Get recommended payment method based on amount and other factors
export const getRecommendedPaymentMethod = (amount: number, userPreferences?: any) => {
  const availableMethods = getAvailablePaymentMethods();
  const config = getPaymentConfig();

  // If COD is not available for this amount, remove it
  if (config.cod.enabled && amount > config.cod.maxAmount) {
    const codIndex = availableMethods.indexOf('cod');
    if (codIndex > -1) {
      availableMethods.splice(codIndex, 1);
    }
  }

  // Return the highest priority available method
  const priorities = getPaymentMethodPriority();
  for (const method of priorities) {
    if (availableMethods.includes(method)) {
      return method;
    }
  }

  return availableMethods[0] || null;
};
