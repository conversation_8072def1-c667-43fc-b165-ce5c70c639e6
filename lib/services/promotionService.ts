// lib/services/promotionService.ts
import { connectToDatabase } from '@/lib/dbconnect';
import { Coupon } from '@/models/Coupon';
import { User } from '@/models/User';
import { GroupOrder } from '@/models/GroupOrder';
import {
  CreateCouponRequest,
  ValidateCouponRequest,
  ApplyCouponRequest,
  BulkCouponGenerationRequest,
  GenerateReferralCodeRequest,
  ProcessReferralRequest,
  EarnLoyaltyPointsRequest,
  RedeemLoyaltyPointsRequest,
  PromotionAnalytics,
  UserReferral,
  UserLoyalty
} from '@/types/promotions';
import mongoose from 'mongoose';

export class PromotionService {

  /**
   * Create a new coupon
   */
  async createCoupon(data: CreateCouponRequest, createdBy: string) {
    await connectToDatabase();

    try {
      // Generate unique code if not provided
      let code = data.code;
      if (!code) {
        code = await Coupon.generateUniqueCode();
      } else {
        // Check if code already exists
        const existing = await Coupon.findOne({ code: code.toUpperCase() });
        if (existing) {
          throw new Error('Coupon code already exists');
        }
      }

      const coupon = new Coupon({
        ...data,
        code: code.toUpperCase(),
        createdBy: new mongoose.Types.ObjectId(createdBy)
      });

      await coupon.save();

      return {
        success: true,
        coupon: coupon.toObject()
      };
    } catch (error) {
      console.error('Error creating coupon:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create coupon'
      };
    }
  }

  /**
   * Validate a coupon for a specific order
   */
  async validateCoupon(data: ValidateCouponRequest) {
    await connectToDatabase();

    try {
      const result = await Coupon.validateCoupon(data.code, data.userId, {
        cartItems: data.cartItems,
        orderTotal: data.orderTotal,
        isGroupOrder: data.isGroupOrder || false,
        groupSize: data.groupSize || 1
      });

      return result;
    } catch (error) {
      console.error('Error validating coupon:', error);
      return {
        valid: false,
        error: 'Error validating coupon'
      };
    }
  }

  /**
   * Apply a coupon to an order
   */
  async applyCoupon(data: ApplyCouponRequest) {
    await connectToDatabase();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // First validate the coupon
      const validation = await this.validateCoupon(data);
      if (!validation.valid) {
        await session.abortTransaction();
        return {
          success: false,
          error: validation.error
        };
      }

      const { coupon, discountAmount } = validation;
      if (!coupon || !discountAmount) {
        await session.abortTransaction();
        return {
          success: false,
          error: 'Invalid coupon data'
        };
      }

      // Increment coupon usage
      await Coupon.incrementUsage(coupon._id.toString(), data.userId);

      // Record coupon usage
      const CouponUsage = mongoose.model('CouponUsage', new mongoose.Schema({
        couponId: { type: mongoose.Schema.Types.ObjectId, ref: 'Coupon', required: true },
        userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
        orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'GroupOrder' },
        discountAmount: { type: Number, required: true },
        orderTotal: { type: Number, required: true },
        usedAt: { type: Date, default: Date.now }
      }));

      await new CouponUsage({
        couponId: coupon._id,
        userId: new mongoose.Types.ObjectId(data.userId),
        orderId: data.orderId ? new mongoose.Types.ObjectId(data.orderId) : undefined,
        discountAmount,
        orderTotal: data.orderTotal
      }).save({ session });

      await session.commitTransaction();

      const finalTotal = Math.max(0, data.orderTotal - discountAmount);

      return {
        success: true,
        discountAmount,
        finalTotal
      };
    } catch (error) {
      await session.abortTransaction();
      console.error('Error applying coupon:', error);
      return {
        success: false,
        error: 'Failed to apply coupon'
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Generate multiple coupons in bulk
   */
  async generateBulkCoupons(data: BulkCouponGenerationRequest, createdBy: string) {
    await connectToDatabase();

    try {
      const coupons = [];
      const errors = [];

      for (let i = 0; i < data.quantity; i++) {
        try {
          const code = await Coupon.generateUniqueCode(
            data.codePrefix || 'BULK',
            data.codeLength || 6
          );

          const coupon = new Coupon({
            ...data.template,
            code,
            createdBy: new mongoose.Types.ObjectId(createdBy)
          });

          await coupon.save();
          coupons.push({ code, id: coupon._id.toString() });
        } catch (error) {
          errors.push(`Failed to generate coupon ${i + 1}: ${error}`);
        }
      }

      return {
        success: true,
        coupons,
        generatedCount: coupons.length,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      console.error('Error generating bulk coupons:', error);
      return {
        success: false,
        error: 'Failed to generate bulk coupons'
      };
    }
  }

  /**
   * Get available coupons for a user
   */
  async getAvailableCoupons(userId: string, filters: any = {}) {
    await connectToDatabase();

    try {
      // Get user's usage history to filter out used coupons
      const CouponUsage = mongoose.model('CouponUsage');
      const userUsage = await CouponUsage.find({ userId }).select('couponId');
      const usedCouponIds = userUsage.map(usage => usage.couponId);

      // Find valid coupons
      const coupons = await Coupon.findValidCoupons({
        isPublic: true,
        ...filters
      });

      // Filter out coupons that user has reached usage limit for
      const availableCoupons = [];
      for (const coupon of coupons) {
        const userUsageCount = userUsage.filter(
          usage => usage.couponId.toString() === coupon._id.toString()
        ).length;

        if (userUsageCount < coupon.userUsageLimit) {
          availableCoupons.push(coupon);
        }
      }

      return availableCoupons;
    } catch (error) {
      console.error('Error getting available coupons:', error);
      throw new Error('Failed to get available coupons');
    }
  }

  /**
   * Generate referral code for user
   */
  async generateReferralCode(data: GenerateReferralCodeRequest) {
    await connectToDatabase();

    try {
      // Check if user already has an active referral code
      const UserReferralModel = mongoose.model('UserReferral', new mongoose.Schema({
        referrerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
        refereeId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
        referralCode: { type: String, required: true, unique: true },
        programId: { type: mongoose.Schema.Types.ObjectId, required: true },
        status: { type: String, enum: ['pending', 'completed', 'rewarded', 'expired'], default: 'pending' },
        refereeFirstPurchase: {
          orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'GroupOrder' },
          amount: Number,
          date: Date
        },
        rewards: {
          referrer: {
            type: String,
            value: Number,
            rewardedAt: Date,
            couponCode: String
          },
          referee: {
            type: String,
            value: Number,
            rewardedAt: Date,
            couponCode: String
          }
        }
      }, { timestamps: true }));

      // Generate unique referral code
      const user = await User.findById(data.userId);
      if (!user) {
        throw new Error('User not found');
      }

      let referralCode;
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        referralCode = `${user.name.substring(0, 3).toUpperCase()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        
        const existing = await UserReferralModel.findOne({ referralCode });
        if (!existing) {
          break;
        }
        attempts++;
      }

      if (attempts >= maxAttempts) {
        throw new Error('Unable to generate unique referral code');
      }

      const referralLink = `${process.env.NEXT_PUBLIC_APP_URL}/signup?ref=${referralCode}`;

      return {
        success: true,
        referralCode,
        referralLink
      };
    } catch (error) {
      console.error('Error generating referral code:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate referral code'
      };
    }
  }

  /**
   * Process a referral when someone signs up
   */
  async processReferral(data: ProcessReferralRequest) {
    await connectToDatabase();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const UserReferralModel = mongoose.model('UserReferral');

      // Find the referrer by referral code
      const existingReferral = await UserReferralModel.findOne({ 
        referralCode: data.referralCode 
      });

      if (existingReferral) {
        await session.abortTransaction();
        return {
          success: false,
          error: 'Referral code already used'
        };
      }

      // Create new referral record
      const referral = new UserReferralModel({
        referrerId: null, // Will be set when we find the referrer
        refereeId: new mongoose.Types.ObjectId(data.refereeId),
        referralCode: data.referralCode,
        programId: new mongoose.Types.ObjectId('000000000000000000000001'), // Default program
        status: 'pending'
      });

      // Generate welcome coupon for referee
      const welcomeCoupon = await this.createCoupon({
        name: 'Welcome Bonus',
        description: 'Welcome bonus for new referral',
        type: 'percentage',
        discountTarget: 'order_total',
        discountValue: 10,
        usageLimit: 1,
        userUsageLimit: 1,
        validFrom: new Date(),
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        isPublic: false,
        stackable: false,
        priority: 1
      }, data.refereeId);

      if (welcomeCoupon.success && welcomeCoupon.coupon) {
        referral.rewards.referee = {
          type: 'coupon',
          value: 10,
          rewardedAt: new Date(),
          couponCode: welcomeCoupon.coupon.code
        };
      }

      await referral.save({ session });
      await session.commitTransaction();

      return {
        success: true,
        referral: referral.toObject(),
        refereeReward: referral.rewards.referee
      };
    } catch (error) {
      await session.abortTransaction();
      console.error('Error processing referral:', error);
      return {
        success: false,
        error: 'Failed to process referral'
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Earn loyalty points for user actions
   */
  async earnLoyaltyPoints(data: EarnLoyaltyPointsRequest) {
    await connectToDatabase();

    try {
      const UserLoyaltyModel = mongoose.model('UserLoyalty', new mongoose.Schema({
        userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
        programId: { type: mongoose.Schema.Types.ObjectId, required: true },
        totalPoints: { type: Number, default: 0 },
        availablePoints: { type: Number, default: 0 },
        currentTier: { type: String, default: 'Bronze' },
        tierProgress: {
          currentPoints: { type: Number, default: 0 },
          nextTierPoints: { type: Number, default: 1000 },
          progressPercentage: { type: Number, default: 0 }
        },
        pointsHistory: [{
          action: String,
          points: Number,
          description: String,
          orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'GroupOrder' },
          expiresAt: Date,
          createdAt: { type: Date, default: Date.now }
        }],
        redemptionHistory: [{
          rewardType: String,
          pointsUsed: Number,
          rewardValue: Number,
          couponCode: String,
          redeemedAt: { type: Date, default: Date.now }
        }],
        lastActivity: { type: Date, default: Date.now }
      }, { timestamps: true }));

      // Calculate points based on action
      let pointsEarned = 0;
      let description = '';

      switch (data.action) {
        case 'purchase':
          pointsEarned = Math.floor((data.orderAmount || 0) / 10); // 1 point per R10 spent
          description = `Purchase order ${data.orderId}`;
          break;
        case 'signup':
          pointsEarned = 100;
          description = 'Welcome bonus for signing up';
          break;
        case 'review':
          pointsEarned = 50;
          description = 'Product review bonus';
          break;
        case 'referral':
          pointsEarned = 200;
          description = 'Successful referral bonus';
          break;
        case 'social_share':
          pointsEarned = 25;
          description = 'Social media share bonus';
          break;
        case 'birthday':
          pointsEarned = 500;
          description = 'Birthday bonus';
          break;
        default:
          pointsEarned = 0;
      }

      if (pointsEarned <= 0) {
        return {
          success: false,
          error: 'No points earned for this action'
        };
      }

      // Find or create user loyalty record
      let userLoyalty = await UserLoyaltyModel.findOne({ userId: data.userId });
      if (!userLoyalty) {
        userLoyalty = new UserLoyaltyModel({
          userId: data.userId,
          programId: new mongoose.Types.ObjectId('000000000000000000000001') // Default program
        });
      }

      // Add points
      userLoyalty.totalPoints += pointsEarned;
      userLoyalty.availablePoints += pointsEarned;
      userLoyalty.lastActivity = new Date();

      // Add to history
      userLoyalty.pointsHistory.push({
        action: data.action,
        points: pointsEarned,
        description,
        orderId: data.orderId ? new mongoose.Types.ObjectId(data.orderId) : undefined,
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year expiration
      });

      // Check for tier updates
      const tierUpdate = this.checkTierUpdate(userLoyalty);

      await userLoyalty.save();

      return {
        success: true,
        pointsEarned,
        totalPoints: userLoyalty.totalPoints,
        tierUpdate
      };
    } catch (error) {
      console.error('Error earning loyalty points:', error);
      return {
        success: false,
        error: 'Failed to earn loyalty points'
      };
    }
  }

  /**
   * Redeem loyalty points for rewards
   */
  async redeemLoyaltyPoints(data: RedeemLoyaltyPointsRequest) {
    await connectToDatabase();

    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const UserLoyaltyModel = mongoose.model('UserLoyalty');
      
      const userLoyalty = await UserLoyaltyModel.findOne({ userId: data.userId });
      if (!userLoyalty) {
        await session.abortTransaction();
        return {
          success: false,
          error: 'User loyalty record not found'
        };
      }

      if (userLoyalty.availablePoints < data.pointsCost) {
        await session.abortTransaction();
        return {
          success: false,
          error: 'Insufficient points'
        };
      }

      // Deduct points
      userLoyalty.availablePoints -= data.pointsCost;

      // Generate reward based on type
      let couponCode;
      let rewardValue = 0;

      switch (data.rewardType) {
        case 'discount':
          rewardValue = data.pointsCost / 10; // 10 points = R1 discount
          const discountCoupon = await this.createCoupon({
            name: 'Loyalty Reward',
            description: `${rewardValue}% discount from loyalty points`,
            type: 'percentage',
            discountTarget: 'order_total',
            discountValue: rewardValue,
            usageLimit: 1,
            userUsageLimit: 1,
            validFrom: new Date(),
            validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            isPublic: false,
            stackable: false,
            priority: 1
          }, data.userId);

          if (discountCoupon.success && discountCoupon.coupon) {
            couponCode = discountCoupon.coupon.code;
          }
          break;

        case 'free_shipping':
          rewardValue = 0;
          const shippingCoupon = await this.createCoupon({
            name: 'Free Shipping Reward',
            description: 'Free shipping from loyalty points',
            type: 'free_shipping',
            discountTarget: 'shipping',
            discountValue: 100,
            usageLimit: 1,
            userUsageLimit: 1,
            validFrom: new Date(),
            validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            isPublic: false,
            stackable: true,
            priority: 1
          }, data.userId);

          if (shippingCoupon.success && shippingCoupon.coupon) {
            couponCode = shippingCoupon.coupon.code;
          }
          break;

        default:
          await session.abortTransaction();
          return {
            success: false,
            error: 'Invalid reward type'
          };
      }

      // Add to redemption history
      userLoyalty.redemptionHistory.push({
        rewardType: data.rewardType,
        pointsUsed: data.pointsCost,
        rewardValue,
        couponCode,
        redeemedAt: new Date()
      });

      await userLoyalty.save({ session });
      await session.commitTransaction();

      return {
        success: true,
        couponCode,
        rewardValue,
        remainingPoints: userLoyalty.availablePoints
      };
    } catch (error) {
      await session.abortTransaction();
      console.error('Error redeeming loyalty points:', error);
      return {
        success: false,
        error: 'Failed to redeem loyalty points'
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Get promotion analytics
   */
  async getPromotionAnalytics(startDate: Date, endDate: Date): Promise<PromotionAnalytics> {
    await connectToDatabase();

    try {
      const CouponUsage = mongoose.model('CouponUsage');

      // Get coupon usage statistics
      const usageStats = await CouponUsage.aggregate([
        {
          $match: {
            usedAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $lookup: {
            from: 'coupons',
            localField: 'couponId',
            foreignField: '_id',
            as: 'coupon'
          }
        },
        { $unwind: '$coupon' },
        {
          $group: {
            _id: null,
            totalUsage: { $sum: 1 },
            totalDiscount: { $sum: '$discountAmount' },
            totalRevenue: { $sum: '$orderTotal' },
            uniqueUsers: { $addToSet: '$userId' }
          }
        }
      ]);

      const stats = usageStats[0] || {
        totalUsage: 0,
        totalDiscount: 0,
        totalRevenue: 0,
        uniqueUsers: []
      };

      // Get top performing coupons
      const topCoupons = await CouponUsage.aggregate([
        {
          $match: {
            usedAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $lookup: {
            from: 'coupons',
            localField: 'couponId',
            foreignField: '_id',
            as: 'coupon'
          }
        },
        { $unwind: '$coupon' },
        {
          $group: {
            _id: '$couponId',
            couponCode: { $first: '$coupon.code' },
            usageCount: { $sum: 1 },
            discountAmount: { $sum: '$discountAmount' },
            revenue: { $sum: '$orderTotal' }
          }
        },
        { $sort: { usageCount: -1 } },
        { $limit: 10 }
      ]);

      return {
        campaignId: 'overall',
        period: { start: startDate, end: endDate },
        metrics: {
          totalImpressions: 0, // Would need tracking
          totalClicks: 0, // Would need tracking
          totalConversions: stats.totalUsage,
          conversionRate: 0, // Would calculate from impressions
          totalRevenue: stats.totalRevenue,
          totalDiscount: stats.totalDiscount,
          roi: stats.totalRevenue > 0 ? ((stats.totalRevenue - stats.totalDiscount) / stats.totalDiscount) * 100 : 0,
          participantCount: stats.uniqueUsers.length,
          averageOrderValue: stats.totalUsage > 0 ? stats.totalRevenue / stats.totalUsage : 0,
          newCustomers: 0, // Would need additional tracking
          returningCustomers: stats.uniqueUsers.length
        },
        topPerformingCoupons: topCoupons.map(coupon => ({
          couponCode: coupon.couponCode,
          usageCount: coupon.usageCount,
          discountAmount: coupon.discountAmount,
          revenue: coupon.revenue
        })),
        userSegmentPerformance: [],
        dailyBreakdown: []
      };
    } catch (error) {
      console.error('Error getting promotion analytics:', error);
      throw new Error('Failed to get promotion analytics');
    }
  }

  // Helper methods
  private checkTierUpdate(userLoyalty: any) {
    const tiers = [
      { name: 'Bronze', requiredPoints: 0, benefits: ['Basic rewards'] },
      { name: 'Silver', requiredPoints: 1000, benefits: ['5% bonus points', 'Free shipping'] },
      { name: 'Gold', requiredPoints: 5000, benefits: ['10% bonus points', 'Priority support'] },
      { name: 'Platinum', requiredPoints: 15000, benefits: ['15% bonus points', 'Exclusive products'] }
    ];

    const currentTier = tiers.find(tier => tier.name === userLoyalty.currentTier);
    const newTier = tiers.reverse().find(tier => userLoyalty.totalPoints >= tier.requiredPoints);

    if (newTier && newTier.name !== userLoyalty.currentTier) {
      const previousTier = userLoyalty.currentTier;
      userLoyalty.currentTier = newTier.name;
      
      // Update tier progress
      const nextTierIndex = tiers.findIndex(tier => tier.name === newTier.name) - 1;
      const nextTier = nextTierIndex >= 0 ? tiers[nextTierIndex] : null;
      
      userLoyalty.tierProgress = {
        currentPoints: userLoyalty.totalPoints,
        nextTierPoints: nextTier ? nextTier.requiredPoints : userLoyalty.totalPoints,
        progressPercentage: nextTier ? 
          Math.min(100, (userLoyalty.totalPoints / nextTier.requiredPoints) * 100) : 100
      };

      return {
        previousTier,
        newTier: newTier.name,
        benefits: newTier.benefits
      };
    }

    return null;
  }
}

// Export singleton instance
export const promotionService = new PromotionService();
