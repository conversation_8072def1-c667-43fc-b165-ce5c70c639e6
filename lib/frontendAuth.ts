// lib/frontendAuth.ts
"use client";

import { User } from "@/types/user";

interface LoginResponse {
  message: string;
  user: User;
  accessTokenExpiresIn: number;
}

interface RegisterResponse {
  message: string;
  user: User;
  accessTokenExpiresIn: number;
}

/**
 * Login user
 */
export async function loginUser(
  email: string,
  password: string,
  rememberMe: boolean
): Promise<{ user: User }> {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',
    },
    body: JSON.stringify({ email, password, rememberMe }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Login failed');
  }

  const data: LoginResponse = await response.json();
  return { user: data.user };
}

/**
 * Login user for shopping (without redirect)
 * This function is similar to loginUser but is specifically for
 * allowing users to login and add products to cart without being redirected
 */
export async function loginUserForShopping(
  email: string,
  password: string,
  rememberMe: boolean = true
): Promise<{ user: User }> {
  // Use the same API endpoint but with a different client-side handling
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',
    },
    body: JSON.stringify({ email, password, rememberMe }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Login failed');
  }

  const data: LoginResponse = await response.json();
  return { user: data.user };
}

/**
 * Register user
 */
// export async function registerUser(
//   name: string,
//   email: string,
//   phone: string,
//   password: string
// ): Promise<{ user: User }> {
//   try {
//     const response = await fetch('/api/users', { // Removed trailing slash for consistency
//       method: 'POST',
//       credentials: 'include',
//       headers: {
//         'Content-Type': 'application/json',
//         'x-client-type': 'web',
//       },
//       body: JSON.stringify({ name, email, phone, password }),
//     });

//     if (!response.ok) {
//       const errorText = await response.text();
//       let errorMessage: string;
//       try {
//         const errorData = JSON.parse(errorText);
//         errorMessage = errorData.error || 'Registration failed';
//       } catch (parseError) {
//         console.error('Error parsing error response:', parseError);
//         errorMessage = errorText || 'Registration failed';
//       }
//       throw new Error(errorMessage);
//     }

//     const data: RegisterResponse = await response.json();
//     return { user: data.user };
//   } catch (error) {
//     console.error('Registration error:', error);
//     if (error instanceof Error) {
//       throw error;
//     } else {
//       throw new Error('An unexpected error occurred during registration');
//     }
//   }
// }



export async function registerUser(
  name: string,
  email: string,
  phone: string,
  password: string,
): Promise<{ user: User }> {
  try {
    const response = await fetch("/api/users", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ name, email, phone, password }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      let errorMessage: string
      try {
        const errorData = JSON.parse(errorText)
        errorMessage = errorData.error || "Registration failed"
      } catch (parseError) {
        console.error("Error parsing error response:", parseError)
        errorMessage = errorText || "Registration failed"
      }
      throw new Error(errorMessage)
    }

    const data: RegisterResponse = await response.json()

    if (!data.user) {
      throw new Error("User data not received from server")
    }

    return { user: data.user }
  } catch (error) {
    console.error("Registration error:", error)
    if (error instanceof Error) {
      throw error
    } else {
      throw new Error("An unexpected error occurred during registration")
    }
  }
}



/**
 * Logout user
 */
export async function logoutUser(userId?: string | null): Promise<void> {
  const response = await fetch('/api/auth/logout', {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',
    },
    body: JSON.stringify({ userId: userId || null }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('Logout failed:', errorData.error || 'Unknown error');
  }
}

/**
 * Get currently logged-in user
 */
export async function getLoggedInUser(): Promise<User | null> {
  const response = await fetch('/api/auth/me', {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'x-client-type': 'web',
    },
  });

  if (!response.ok) {
    return null;
  }

  const data = await response.json();
  return data.user as User;
}
