"use client";

import { User } from "@/types/user";
import { StokvelGroup } from "@/types/stokvelgroup"; 
// (Define these types if you haven't already.)

/**
 * 1) Check if a user by email exists in the DB
 * - Returns user object or null
 */
export async function checkUserByEmail(email: string): Promise<User | null> {
  try {
    const res = await fetch(`/api/users/find-by-email?email=${encodeURIComponent(email)}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
    });

    if (!res.ok) {
      if (res.status === 404) return null; // user not found
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to check user");
    }

    // If found, parse the user
    const data = await res.json();
    // e.g. { user: {...} }
    return data.user as User;
  } catch (error) {
    console.error("Error checking user by email:", error);
    throw error;
  }
}

/**
 * 2) Join a stokvel group
 * - Expects userId, groupId
 * - Example: /api/stokvel-groups/join
 */
export async function joinGroup(userId: string, groupId: string): Promise<StokvelGroup> {
  try {
    const res = await fetch("/api/stokvel-groups/join", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ userId, groupId }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to join group");
    }

    const data = await res.json();
    // e.g. { group: {...} }
    return data.group as StokvelGroup;
  } catch (error) {
    console.error("Error joining group:", error);
    throw error;
  }
}

/**
 * 3) Store a location request if no group is found
 * - Admin can later see these requests
 */
export async function storeLocationRequest(location: string): Promise<void> {
  try {
    const res = await fetch("/api/location-requests", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ location }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.error || "Failed to store location request");
    }
    // success => do nothing
  } catch (error) {
    console.error("Error storing location request:", error);
    throw error;
  }
}

/**
 * 4) Example register user
 * - You already have registerUser in `frontendAuth.ts`
 * - If you need an alternative, adapt or skip
 */
export async function registerUser(name: string, email: string, phone: string, password: string): Promise<User> {
  try {
    const res = await fetch("/api/users", {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "x-client-type": "web",
      },
      body: JSON.stringify({ name, email, phone, password }),
    });

    if (!res.ok) {
      const errData = await res.json();
      throw new Error(errData.error || "Failed to register user");
    }

    const data = await res.json();
    return data.user as User;
  } catch (error) {
    console.error("Error registering user:", error);
    throw error;
  }
}


