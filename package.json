{"name": "stockvelcommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_ENV=production next build", "build:dev": "next build", "start": "next start", "lint": "next lint", "lint:prod": "NODE_ENV=production next lint", "type-check": "tsc --noEmit", "type-check:prod": "tsc --noEmit --project tsconfig.prod.json", "prebuild": "rimraf .next"}, "dependencies": {"@ai-sdk/openai": "^1.1.10", "@hookform/resolvers": "^3.10.0", "@langchain/community": "^0.3.36", "@langchain/core": "^0.3.40", "@langchain/openai": "^0.4.4", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.7", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-devtools": "^5.64.1", "ai": "^4.1.36", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "formidable": "^3.5.2", "framer-motion": "^11.18.2", "googleapis": "^144.0.0", "ioredis": "^5.5.0", "joi": "^17.13.3", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.19", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "mongodb": "^6.12.0", "mongoose": "^8.9.5", "next": "15.1.3", "nodemailer": "^6.9.16", "openai": "^4.84.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "recharts": "^2.10.0", "sonner": "^1.4.3", "swr": "^2.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.0", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}