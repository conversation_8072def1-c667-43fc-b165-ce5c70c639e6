// // 


// "use client"

// import { useState, type ReactElement } from "react"

// export const useMultiStepForm = <T extends Record<string, string>>(steps: ReactElement[], initialData: T) => {
//   const [currentStepIndex, setCurrentStepIndex] = useState(0)
//   const [formData, setFormData] = useState<T>(initialData)

//   function next() {
//     setCurrentStepIndex((i) => {
//       if (i >= steps.length - 1) return i
//       return i + 1
//     })
//   }

//   function back() {
//     setCurrentStepIndex((i) => {
//       if (i <= 0) return i
//       return i - 1
//     })
//   }

//   function goTo(index: number) {
//     setCurrentStepIndex(index)
//   }

//   const updateFormData = (key: keyof T, value: string) => {
//     setFormData((prev) => ({ ...prev, [key]: value }))
//   }

//   return {
//     currentStepIndex,
//     step: steps[currentStepIndex],
//     steps,
//     isFirstStep: currentStepIndex === 0,
//     isLastStep: currentStepIndex === steps.length - 1,
//     goTo,
//     next,
//     back,
//     formData,
//     updateFormData,
//   }
// }


"use client"

import { useState, useCallback, type ReactElement } from "react"

export const useMultiStepForm = <T extends Record<string, any>>(initialSteps: ReactElement[], initialData: T) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [steps, setSteps] = useState(initialSteps)
  const [formData, setFormData] = useState<T>(initialData)

  const next = useCallback(() => {
    setCurrentStepIndex((i) => {
      if (i >= steps.length - 1) return i
      return i + 1
    })
  }, [steps.length])

  const back = useCallback(() => {
    setCurrentStepIndex((i) => {
      if (i <= 0) return i
      return i - 1
    })
  }, [])

  const goTo = useCallback((index: number) => {
    setCurrentStepIndex(index)
  }, [])

  const updateFormData = useCallback((newData: Partial<T>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }, [])

  return {
    currentStepIndex,
    step: steps[currentStepIndex],
    steps,
    isFirstStep: currentStepIndex === 0,
    isLastStep: currentStepIndex === steps.length - 1,
    goTo,
    next,
    back,
    formData,
    updateFormData,
    setSteps,
  }
}
