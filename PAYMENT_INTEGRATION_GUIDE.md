# 🏦 PayFast Payment Integration Guide for StokvelMarket

## 📋 **Overview**

PayFast is South Africa's leading payment gateway, often referred to as "South Africa's PayPal." This guide provides step-by-step instructions for integrating PayFast into our StokvelMarket application using their API and hosted payment solutions.

## 🎯 **Why PayFast?**

### **Advantages for South African Market:**
- ✅ **Local Expertise**: Designed specifically for South African businesses
- ✅ **Multiple Payment Methods**: Credit cards, EFT, instant EFT, Bitcoin
- ✅ **No Setup Fees**: Only pay for successful transactions
- ✅ **Fraud Protection**: Built-in fraud detection and prevention
- ✅ **Mobile Optimized**: Responsive payment pages for mobile users
- ✅ **Recurring Billing**: Perfect for subscription-based services
- ✅ **Local Support**: South African customer support team

### **Supported Payment Methods:**
- Credit Cards (Visa, Mastercard, American Express)
- Instant EFT (Major South African banks)
- Standard Bank Instant Money
- Nedbank Pay-by-Link
- Bitcoin payments
- Mobicred
- SnapScan

## 🔧 **Integration Options**

### **1. Custom Integration (Recommended for StokvelMarket)**
- Full control over checkout experience
- API-based integration with our Next.js backend
- Seamless user experience within our application

### **2. Hosted Payment Page**
- PayFast handles the entire payment process
- Users redirected to PayFast's secure payment page
- Simpler integration but less control

### **3. Onsite Payments**
- Payment form embedded within our site
- PayFast handles security while maintaining our branding
- Best of both worlds approach

## 🚀 **Step-by-Step Integration**

### **Phase 1: Account Setup**

#### **1.1 Create PayFast Account**
```bash
# Development/Testing
1. Visit: https://sandbox.payfast.co.za
2. Register for sandbox account
3. Verify email and complete setup

# Production
1. Visit: https://www.payfast.co.za
2. Complete business registration
3. Provide required documentation
4. Wait for account approval
```

#### **1.2 Obtain API Credentials**
```javascript
// Sandbox Credentials (for testing)
const PAYFAST_CONFIG = {
  merchant_id: "********",      // Sandbox merchant ID
  merchant_key: "46f0cd694581a", // Sandbox merchant key
  passphrase: "jt7NOE43FZPn",   // Optional passphrase
  sandbox: true
};

// Production Credentials (from your PayFast dashboard)
const PAYFAST_PRODUCTION = {
  merchant_id: "YOUR_MERCHANT_ID",
  merchant_key: "YOUR_MERCHANT_KEY", 
  passphrase: "YOUR_PASSPHRASE",
  sandbox: false
};
```

### **Phase 2: Environment Configuration**

#### **2.1 Environment Variables**
```bash
# .env.local
PAYFAST_MERCHANT_ID=********
PAYFAST_MERCHANT_KEY=46f0cd694581a
PAYFAST_PASSPHRASE=jt7NOE43FZPn
PAYFAST_SANDBOX=true
PAYFAST_RETURN_URL=http://localhost:3001/payment/success
PAYFAST_CANCEL_URL=http://localhost:3001/payment/cancel
PAYFAST_NOTIFY_URL=http://localhost:3001/api/payment/payfast/notify
```

#### **2.2 Install Dependencies**
```bash
npm install crypto-js
npm install @types/crypto-js --save-dev
```

### **Phase 3: Backend Implementation**

#### **3.1 PayFast Service Class**
```typescript
// lib/services/payfastService.ts
import crypto from 'crypto';

export class PayFastService {
  private merchantId: string;
  private merchantKey: string;
  private passphrase: string;
  private sandbox: boolean;

  constructor() {
    this.merchantId = process.env.PAYFAST_MERCHANT_ID!;
    this.merchantKey = process.env.PAYFAST_MERCHANT_KEY!;
    this.passphrase = process.env.PAYFAST_PASSPHRASE!;
    this.sandbox = process.env.PAYFAST_SANDBOX === 'true';
  }

  // Generate payment signature
  generateSignature(data: Record<string, any>): string {
    // Remove signature if present
    delete data.signature;
    
    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
      .join('&');
    
    // Add passphrase if set
    const stringToHash = this.passphrase 
      ? `${paramString}&passphrase=${encodeURIComponent(this.passphrase)}`
      : paramString;
    
    // Generate MD5 hash
    return crypto.createHash('md5').update(stringToHash).digest('hex');
  }

  // Create payment data
  createPaymentData(orderData: {
    orderId: string;
    amount: number;
    description: string;
    customerEmail: string;
    customerName: string;
  }) {
    const data = {
      merchant_id: this.merchantId,
      merchant_key: this.merchantKey,
      return_url: process.env.PAYFAST_RETURN_URL,
      cancel_url: process.env.PAYFAST_CANCEL_URL,
      notify_url: process.env.PAYFAST_NOTIFY_URL,
      name_first: orderData.customerName.split(' ')[0],
      name_last: orderData.customerName.split(' ').slice(1).join(' '),
      email_address: orderData.customerEmail,
      m_payment_id: orderData.orderId,
      amount: orderData.amount.toFixed(2),
      item_name: orderData.description,
      item_description: orderData.description,
      custom_int1: orderData.orderId,
      custom_str1: 'StokvelMarket'
    };

    // Generate signature
    data.signature = this.generateSignature(data);
    
    return data;
  }

  // Get payment URL
  getPaymentUrl(): string {
    return this.sandbox 
      ? 'https://sandbox.payfast.co.za/eng/process'
      : 'https://www.payfast.co.za/eng/process';
  }

  // Verify payment notification
  verifyPayment(postData: Record<string, any>): boolean {
    const signature = postData.signature;
    delete postData.signature;
    
    const generatedSignature = this.generateSignature(postData);
    return signature === generatedSignature;
  }
}
```

#### **3.2 Payment API Routes**

```typescript
// app/api/payment/payfast/create/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/lib/services/payfastService';
import { verifyAccessToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { orderId, amount, description, customerEmail, customerName } = body;

    // Validate required fields
    if (!orderId || !amount || !customerEmail) {
      return NextResponse.json({ 
        error: 'Missing required fields' 
      }, { status: 400 });
    }

    const payfast = new PayFastService();
    const paymentData = payfast.createPaymentData({
      orderId,
      amount,
      description: description || 'StokvelMarket Purchase',
      customerEmail,
      customerName: customerName || 'Customer'
    });

    return NextResponse.json({
      success: true,
      paymentData,
      paymentUrl: payfast.getPaymentUrl()
    });

  } catch (error) {
    console.error('PayFast payment creation error:', error);
    return NextResponse.json({ 
      error: 'Failed to create payment' 
    }, { status: 500 });
  }
}
```

```typescript
// app/api/payment/payfast/notify/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/lib/services/payfastService';
import { updateOrderPaymentStatus } from '@/lib/services/orderService';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const postData: Record<string, any> = {};
    
    // Convert FormData to object
    formData.forEach((value, key) => {
      postData[key] = value.toString();
    });

    const payfast = new PayFastService();
    
    // Verify payment signature
    if (!payfast.verifyPayment(postData)) {
      console.error('PayFast signature verification failed');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Process payment based on status
    const { payment_status, m_payment_id, amount_gross } = postData;
    
    if (payment_status === 'COMPLETE') {
      // Update order status to paid
      await updateOrderPaymentStatus(m_payment_id, {
        status: 'paid',
        transactionId: postData.pf_payment_id,
        amount: parseFloat(amount_gross),
        paymentMethod: 'payfast',
        paidAt: new Date()
      });

      console.log(`Payment completed for order: ${m_payment_id}`);
    }

    // Always return 200 OK to PayFast
    return NextResponse.json({ status: 'OK' });

  } catch (error) {
    console.error('PayFast notification error:', error);
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 });
  }
}
```

### **Phase 4: Frontend Implementation**

#### **4.1 Payment Component**
```typescript
// components/payment/PayFastCheckout.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreditCard, Shield, Zap } from 'lucide-react';

interface PayFastCheckoutProps {
  orderId: string;
  amount: number;
  description: string;
  customerEmail: string;
  customerName: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function PayFastCheckout({
  orderId,
  amount,
  description,
  customerEmail,
  customerName,
  onSuccess,
  onError
}: PayFastCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handlePayment = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/payment/payfast/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          orderId,
          amount,
          description,
          customerEmail,
          customerName
        })
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Payment creation failed');
      }

      // Create form and submit to PayFast
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = data.paymentUrl;
      form.style.display = 'none';

      // Add all payment data as hidden inputs
      Object.entries(data.paymentData).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value as string;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();

    } catch (error) {
      console.error('Payment error:', error);
      onError?.(error instanceof Error ? error.message : 'Payment failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Secure Payment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <p className="text-2xl font-bold">R{amount.toFixed(2)}</p>
          <p className="text-sm text-gray-600">{description}</p>
        </div>

        <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Shield className="h-4 w-4" />
            <span>Secure</span>
          </div>
          <div className="flex items-center gap-1">
            <Zap className="h-4 w-4" />
            <span>Instant</span>
          </div>
        </div>

        <Button 
          onClick={handlePayment}
          disabled={isLoading}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          {isLoading ? 'Processing...' : 'Pay with PayFast'}
        </Button>

        <div className="text-xs text-center text-gray-500">
          Powered by PayFast - South Africa's trusted payment gateway
        </div>
      </CardContent>
    </Card>
  );
}
```

### **Phase 5: Success/Cancel Pages**

#### **5.1 Payment Success Page**
```typescript
// app/payment/success/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CheckCircle, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const [orderDetails, setOrderDetails] = useState(null);

  useEffect(() => {
    // Extract payment details from URL parameters
    const paymentId = searchParams.get('pf_payment_id');
    const orderId = searchParams.get('m_payment_id');
    
    if (orderId) {
      // Fetch order details
      fetchOrderDetails(orderId);
    }
  }, [searchParams]);

  const fetchOrderDetails = async (orderId: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`);
      const data = await response.json();
      setOrderDetails(data);
    } catch (error) {
      console.error('Error fetching order details:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-600">Payment Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            Your payment has been processed successfully. You will receive a confirmation email shortly.
          </p>
          
          {orderDetails && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">Order ID</p>
              <p className="font-mono text-sm">{orderDetails.orderNumber}</p>
            </div>
          )}

          <div className="space-y-2">
            <Button asChild className="w-full">
              <Link href="/profile/orders">
                View Orders
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link href="/">Continue Shopping</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🔒 **Security Considerations**

### **1. Signature Verification**
- Always verify PayFast signatures on notifications
- Use HTTPS for all payment-related endpoints
- Validate all incoming payment data

### **2. Environment Security**
```bash
# Production Environment Variables
PAYFAST_MERCHANT_ID=your_production_merchant_id
PAYFAST_MERCHANT_KEY=your_production_merchant_key
PAYFAST_PASSPHRASE=your_strong_passphrase
PAYFAST_SANDBOX=false
```

### **3. Webhook Security**
- Implement IP whitelisting for PayFast notifications
- Log all payment notifications for audit trails
- Handle duplicate notifications gracefully

## 📊 **Testing Strategy**

### **1. Sandbox Testing**
```javascript
// Test card numbers for sandbox
const TEST_CARDS = {
  visa: '****************',
  mastercard: '****************',
  amex: '***************'
};
```

### **2. Integration Testing**
- Test successful payments
- Test failed payments
- Test cancelled payments
- Test webhook notifications
- Test signature verification

## 🚀 **Go-Live Checklist**

### **Pre-Production**
- [ ] Complete PayFast business verification
- [ ] Update environment variables to production
- [ ] Test all payment flows in sandbox
- [ ] Implement proper error handling
- [ ] Set up monitoring and logging

### **Production**
- [ ] Switch to production endpoints
- [ ] Monitor first transactions closely
- [ ] Set up payment reconciliation
- [ ] Configure fraud monitoring
- [ ] Train support team on payment issues

## 📈 **Next Steps**

1. **Implement PayFast Service**: Start with the PayFastService class
2. **Create API Routes**: Set up payment creation and notification endpoints
3. **Build Frontend Components**: Create payment forms and success pages
4. **Test Integration**: Thoroughly test in sandbox environment
5. **Deploy to Production**: Switch to production credentials and go live

This integration will provide StokvelMarket with a robust, South African-focused payment solution that supports multiple payment methods and provides excellent user experience for local customers.

## 🔄 **Advanced Features**

### **1. Recurring Payments (Subscriptions)**

#### **Subscription Setup**
```typescript
// lib/services/payfastSubscriptionService.ts
export class PayFastSubscriptionService extends PayFastService {
  createSubscriptionData(subscriptionData: {
    orderId: string;
    amount: number;
    frequency: number; // 3 = monthly, 6 = bi-annually
    cycles: number; // 0 = indefinite, >0 = specific cycles
    customerEmail: string;
    customerName: string;
  }) {
    const data = {
      ...this.createPaymentData({
        orderId: subscriptionData.orderId,
        amount: subscriptionData.amount,
        description: 'StokvelMarket Subscription',
        customerEmail: subscriptionData.customerEmail,
        customerName: subscriptionData.customerName
      }),
      subscription_type: 1, // Enable subscription
      billing_date: new Date().toISOString().split('T')[0],
      recurring_amount: subscriptionData.amount.toFixed(2),
      frequency: subscriptionData.frequency,
      cycles: subscriptionData.cycles
    };

    // Regenerate signature with subscription data
    data.signature = this.generateSignature(data);
    return data;
  }

  // Cancel subscription
  async cancelSubscription(token: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.getApiUrl()}/subscriptions/${token}/cancel`, {
        method: 'PUT',
        headers: {
          'merchant-id': this.merchantId,
          'version': 'v1',
          'timestamp': new Date().toISOString(),
          'signature': this.generateApiSignature('PUT', `/subscriptions/${token}/cancel`)
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Subscription cancellation error:', error);
      return false;
    }
  }

  private getApiUrl(): string {
    return this.sandbox
      ? 'https://api.sandbox.payfast.co.za'
      : 'https://api.payfast.co.za';
  }
}
```

### **2. Group Payment Splitting**

#### **Split Payment Implementation**
```typescript
// lib/services/groupPaymentService.ts
export class GroupPaymentService {
  async createGroupPayment(groupOrder: {
    groupId: string;
    totalAmount: number;
    memberContributions: Array<{
      userId: string;
      amount: number;
      email: string;
      name: string;
    }>;
  }) {
    const payfast = new PayFastService();
    const payments = [];

    for (const member of groupOrder.memberContributions) {
      const paymentData = payfast.createPaymentData({
        orderId: `${groupOrder.groupId}-${member.userId}-${Date.now()}`,
        amount: member.amount,
        description: `Group Order Contribution - ${groupOrder.groupId}`,
        customerEmail: member.email,
        customerName: member.name
      });

      payments.push({
        userId: member.userId,
        paymentData,
        paymentUrl: payfast.getPaymentUrl()
      });
    }

    return payments;
  }

  async trackGroupPaymentProgress(groupId: string) {
    // Implementation to track which members have paid
    // Update group order status when all payments complete
  }
}
```

### **3. Mobile Money Integration**

#### **Mobile Payment Options**
```typescript
// components/payment/MobilePaymentOptions.tsx
export function MobilePaymentOptions({ amount, orderId }: {
  amount: number;
  orderId: string;
}) {
  const mobileOptions = [
    {
      name: 'SnapScan',
      icon: '/icons/snapscan.png',
      description: 'Scan QR code to pay'
    },
    {
      name: 'Instant EFT',
      icon: '/icons/eft.png',
      description: 'Pay directly from your bank'
    },
    {
      name: 'Mobicred',
      icon: '/icons/mobicred.png',
      description: 'Buy now, pay later'
    }
  ];

  return (
    <div className="space-y-3">
      <h3 className="font-semibold">Mobile Payment Options</h3>
      {mobileOptions.map((option) => (
        <button
          key={option.name}
          className="w-full p-3 border rounded-lg hover:bg-gray-50 flex items-center gap-3"
          onClick={() => initiatePayment(option.name)}
        >
          <img src={option.icon} alt={option.name} className="w-8 h-8" />
          <div className="text-left">
            <p className="font-medium">{option.name}</p>
            <p className="text-sm text-gray-600">{option.description}</p>
          </div>
        </button>
      ))}
    </div>
  );
}
```

### **4. Payment Analytics Dashboard**

#### **Payment Metrics Component**
```typescript
// components/admin/PaymentAnalytics.tsx
export function PaymentAnalytics() {
  const [metrics, setMetrics] = useState({
    totalRevenue: 0,
    successfulPayments: 0,
    failedPayments: 0,
    averageOrderValue: 0,
    paymentMethods: {}
  });

  useEffect(() => {
    fetchPaymentMetrics();
  }, []);

  const fetchPaymentMetrics = async () => {
    try {
      const response = await fetch('/api/admin/payment-analytics');
      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      console.error('Error fetching payment metrics:', error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MetricCard
        title="Total Revenue"
        value={`R${metrics.totalRevenue.toLocaleString()}`}
        icon={<DollarSign className="h-6 w-6" />}
        trend="+12.5%"
      />
      <MetricCard
        title="Successful Payments"
        value={metrics.successfulPayments.toString()}
        icon={<CheckCircle className="h-6 w-6" />}
        trend="+8.2%"
      />
      <MetricCard
        title="Failed Payments"
        value={metrics.failedPayments.toString()}
        icon={<XCircle className="h-6 w-6" />}
        trend="-2.1%"
      />
      <MetricCard
        title="Average Order Value"
        value={`R${metrics.averageOrderValue.toFixed(2)}`}
        icon={<TrendingUp className="h-6 w-6" />}
        trend="+5.7%"
      />
    </div>
  );
}
```

## 🛠 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **1. Signature Mismatch**
```typescript
// Debug signature generation
function debugSignature(data: Record<string, any>) {
  console.log('Original data:', data);

  const sortedKeys = Object.keys(data).sort();
  console.log('Sorted keys:', sortedKeys);

  const paramString = sortedKeys
    .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
    .join('&');
  console.log('Parameter string:', paramString);

  // Check if passphrase is being added correctly
  const withPassphrase = `${paramString}&passphrase=${encodeURIComponent(passphrase)}`;
  console.log('With passphrase:', withPassphrase);

  const signature = crypto.createHash('md5').update(withPassphrase).digest('hex');
  console.log('Generated signature:', signature);

  return signature;
}
```

#### **2. Webhook Not Receiving Notifications**
```typescript
// Test webhook endpoint
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'Webhook endpoint is working',
    timestamp: new Date().toISOString()
  });
}

// Add logging to webhook
export async function POST(request: NextRequest) {
  console.log('Webhook received:', {
    headers: Object.fromEntries(request.headers.entries()),
    url: request.url,
    timestamp: new Date().toISOString()
  });

  // ... rest of webhook logic
}
```

#### **3. Payment Status Not Updating**
```typescript
// Add retry mechanism for payment updates
async function updatePaymentWithRetry(orderId: string, paymentData: any, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await updateOrderPaymentStatus(orderId, paymentData);
      console.log(`Payment updated successfully on attempt ${attempt}`);
      return;
    } catch (error) {
      console.error(`Payment update attempt ${attempt} failed:`, error);

      if (attempt === maxRetries) {
        // Log to error tracking service
        console.error('All payment update attempts failed:', error);
        throw error;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

## 📱 **Mobile Optimization**

### **Responsive Payment Forms**
```typescript
// components/payment/ResponsivePaymentForm.tsx
export function ResponsivePaymentForm() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className={`payment-form ${isMobile ? 'mobile' : 'desktop'}`}>
      {isMobile ? (
        <MobilePaymentInterface />
      ) : (
        <DesktopPaymentInterface />
      )}
    </div>
  );
}
```

## 🔐 **Security Best Practices**

### **1. PCI Compliance**
- Never store credit card information
- Use HTTPS for all payment pages
- Implement proper input validation
- Regular security audits

### **2. Fraud Prevention**
```typescript
// lib/services/fraudDetectionService.ts
export class FraudDetectionService {
  async analyzeTransaction(transactionData: {
    amount: number;
    userEmail: string;
    ipAddress: string;
    userAgent: string;
  }) {
    const riskScore = await this.calculateRiskScore(transactionData);

    if (riskScore > 80) {
      return {
        approved: false,
        reason: 'High risk transaction',
        requiresManualReview: true
      };
    }

    return { approved: true, riskScore };
  }

  private async calculateRiskScore(data: any): Promise<number> {
    let score = 0;

    // Check for unusual amounts
    if (data.amount > 10000) score += 20;

    // Check for suspicious patterns
    // ... implement fraud detection logic

    return score;
  }
}
```

## 📊 **Performance Monitoring**

### **Payment Performance Metrics**
```typescript
// lib/services/paymentMonitoringService.ts
export class PaymentMonitoringService {
  async trackPaymentPerformance(paymentData: {
    orderId: string;
    amount: number;
    paymentMethod: string;
    processingTime: number;
    success: boolean;
  }) {
    // Send metrics to monitoring service
    await this.sendMetrics({
      metric: 'payment_processing_time',
      value: paymentData.processingTime,
      tags: {
        payment_method: paymentData.paymentMethod,
        success: paymentData.success.toString()
      }
    });
  }

  async generatePaymentReport(dateRange: { start: Date; end: Date }) {
    // Generate comprehensive payment reports
    return {
      totalTransactions: 0,
      successRate: 0,
      averageProcessingTime: 0,
      revenueByMethod: {},
      failureReasons: {}
    };
  }
}
```

This comprehensive guide provides everything needed to implement a production-ready PayFast integration for StokvelMarket, including advanced features, security considerations, and monitoring capabilities.
